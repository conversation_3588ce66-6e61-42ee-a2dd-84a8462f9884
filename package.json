{"name": "jjsim-test", "main": "background.js", "version": "4.0.0", "currentVersion": "*******", "author": "jjs", "env": "localtest", "otherEnv": "local、dev、localtest、onlinetest、tyy", "description": "JJS IM", "domain": "im.leyoujia.com", "nodejs": true, "node-remote": "http://localhost:8888/*", "single-instance": true, "webkit": {"page-cache": false}, "user-agent": "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36 leyoujiaIm/*******", "proxyInfo": "IDCnSJfuSjRbnq0aZCOzNO2FBcRCDZX9JojFvKBA5ZQZ5e3gwHJqpgHnt5MeDaQM3p6FJX7EdQkNryF//L6rPB2iBaxxJBhoiyBDpQWhjUhtDLKqbihDsXZHa3Bzqrd2UlEMiEHOtpiKAussiTLIsnGhNCIamiF/wCY5pZ1b1oHYzRRILHG+VVbPzZP1pd87CY5NPoJJPw71/x/OtmBwV3gXuU2Uv7tFq+3ARUT2jV8rS/8d1JAwXjWPqrmKlZVNlj5CX4Z4OgmXile1u9iTmNtjVDIDHPX8otsrWO/VHMZaYnK5H02qvTk1KFP8qrdtQNVVGKSlaM9SaeKbXz/QssvsgpwesCKEWj3rz+65Fu+AU6yDMHUTptnwWB4zkiEKItLwkgLXrWwKiv+lgKUe4vL08ovhJhhwkPTK73ITIKIE0x96R1t3q3Q23Drkhd36bIeiZzGcxbrAFK8F72eXHMPb7iXh5SP6fie8zRMJdtKxT+px33raagBAcazU2hGZSsmc06P9veONKqMOA41dyQ==", "chromium-args": "--disable-gpu --disable-features=CalculateNativeWinOcclusion --ignore-certificate-errors --disable-background-timer-throttling", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build && node buildFile.js", "buildFile": "node buildFile.js", "buildVue": "vue-cli-service build", "report": "vue-cli-service build --report"}, "window": {"width": 455, "height": 350, "min_width": 455, "min_height": 350, "max_width": 455, "max_height": 350, "position": "center", "title": "乐聊", "icon": "./jjs_im_icon.png", "show": true, "show_in_taskbar": true, "toolbar": true, "frame": false, "transparent": true, "resizable": false}, "dependencies": {"adm-zip": "^0.5.9", "axios": "^0.21.4", "core-js": "^3.6.5", "dexie": "^3.2.0", "heic2any": "0.0.4", "jsqr": "^1.4.0", "lodash": "^4.17.21", "mime": "^3.0.0", "qrcode": "^1.4.4", "vue": "^3.2.26", "vue-router": "^4.0.0-0", "vuex": "^4.0.0-0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "archiver": "^5.3.0", "sass": "^1.26.5", "sass-loader": "^8.0.2", "terser": "^5.17.6"}}