const path = require("path")
const fs = require("fs")
const webpack = require("webpack");

let version = getArg("version");
let env = getArg("env");

// 获取参数
function getArg(key) {
  let argv = JSON.parse(process.env.npm_config_argv || "{}").remain || [];
  let index = argv.findIndex(item => new RegExp(key).test(item))
  if (index != -1) {
    return argv[index].split("=")[1]
  }
  return ""
}

module.exports = {
  publicPath: "./",
  outputDir: "./dist/win-ia32-unpacked",
  productionSourceMap: false,
  //修改端口号
  devServer: {
    port: 8888
  },
  css: {
    loaderOptions: {
      sass: {
        prependData: `@import "@static/css/public.scss";`
      }
    }
  },
  configureWebpack: {
    optimization: {
      splitChunks: {
        chunks: "all",
        cacheGroups: {
          iconvLite: {
            test: /[\\/]node_modules[\\/](iconv-lite)[\\/]/,
            name: "chunk-iconv",
            priority: 2
          },
          dexie: {
            test: /[\\/]node_modules[\\/](dexie)[\\/]/,
            name: "chunk-dexie",
            priority: 2
          },
          vendors: {
            test: /[\\/]node_modules[\\/]/,
            name: "chunk-vendors",
            priority: 1,
            reuseExistingChunk: true
          },
          common: {
            chunks: "all",
            name: "chunk-common",
            priority: 1,
            reuseExistingChunk: true
          }
        }
      },
    },
    plugins: [
      // new webpack.optimize.LimitChunkCountPlugin({
      //   maxChunks: 1,
      // }),
      function () {
        // 打包开始修改package文件的版本号
        this.plugin("entryOption", function () {
          if (getArg("version")) {
            let pkgPath = path.join(__dirname, "/package.json");
            let pkg = fs.readFileSync(pkgPath);
            pkg = JSON.parse(pkg);
            pkg.currentVersion = version;
            pkg.env = env;
            pkg.name = "jjsim" + (env == "online" ? "" : `-${env}`);
            pkg["user-agent"] = `Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36 leyoujiaIm/${version}`;
            if (env == "online") {
              delete pkg.dependencies;
              delete pkg.devDependencies;
            }
            fs.writeFileSync(pkgPath, JSON.stringify(pkg, null, 2));
          }
        })
      },
    ],
    resolve: {
      alias: {
        "@store": path.join(__dirname, "src/store"),
        "@static": path.join(__dirname, "src/assets"),
        "@view": path.join(__dirname, "src/views"),
        "@comp": path.join(__dirname, "src/components"),
        "@utils": path.join(__dirname, "src/utils"),
        "@dire": path.join(__dirname, "src/directives"),
        "@public": path.join(__dirname, "public"),
      }
    }
  },
  chainWebpack: config => {
    if (process.env.NODE_ENV === "production") {
      // 清除css，js版本号
      config.output.filename("[name].js").end();
      config.output.chunkFilename("[name].js").end();
      // 为生产环境修改配置...
      config.plugin("extract-css").tap(args => [{
        filename: `[name].css`,
        chunkFilename: `[name].css`
      }]);
    }
  },
  pages: {
    index: {
      template: "public/index.html",
      entry: "src/main.js",
      chunks: ["index", "chunk-index", "chunk-child", "chunk-vendors", "chunk-common", "chunk-iconv", "chunk-dexie"]
    },
    forward: {
      template: "public/forward.html",
      entry: "child/forward/main.js",
      chunks: ["forward", "chunk-common", "chunk-iconv"]
    },
    viewer: {
      template: "public/viewer.html",
      entry: "child/viewer/main.js",
      chunks: ["viewer", "chunk-common", "chunk-iconv"]
    },
    notify: {
      template: "public/notify.html",
      entry: "child/notify/main.js",
      chunks: ["notify", "chunk-common"]
    },
    shortcutConflict: {
      template: "public/shortcutConflict.html",
      entry: "child/shortcutConflict/main.js",
      chunks: ["shortcutConflict", "chunk-common"]
    },
    netDetect: {
      template: "public/netDetect.html",
      entry: "child/netDetect/main.js",
      chunks: ["netDetect", "chunk-common", "chunk-iconv"]
    }
  }
};
