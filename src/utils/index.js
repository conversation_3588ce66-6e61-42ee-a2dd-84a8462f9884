/**
 * 通用方法
 * enCodeJJS-加密编码
 * randomString-随机生成字符串
 * createQrCode-生成二维码
 * getAllComputerInfo-获取电脑基本信息
 * dealMem-内存单位转换
 * debounce-输入框防抖
 * throttle-输入节流
 * dateFormat-时间格式化 yyyy-mm-dd
 * transTime-时间格式化  昨天、今天、时间
 * downloadFile-下载文件
 * getLocalFile-获取本地文件file对象
 * saveImageLocal-缓存图片到本地
 * saveBase64Local-保存base64到本地
 * getFileCachedPath-获取文件缓存路径
 * getAppPath-获取app运行路径
 * mkdirsSync-同步递归创建目录
 * imgBase64ToBuffer-图片base64转文件buffer
 * existFileName-判断文件是否存在并修改返回
 * compareVersion-比较版本号
 * encrypt-aes-128-cbc加密
 * decrypt-aes-128-cbc解密
 * btoa-btoa加密
 * htmlEscapeAll-html转义
 * htmlUnEscapeAll-html去除转义
 * strToHtml-换行解析
 * getHighlight-文字高亮html
 * getLink-链接格式化html
 * linkFormat-链接格式化
 * getAvatar-获取用户头像，用于数据源头，不用于img标签
 * avatarError-头像加载失败获取本地默认头像
 * fangError-房源图片加载错误
 * setUserBaseInfo-设置用户基础信息
 * getSessionType-获取会话类型
 * selElm-选中元素
 * secToTime-毫秒数转时分秒
 * getInputMessage-获取输入框数据
 * dealInputMessage-整理输入框数据
 * dataUrlToBlob-dataUrl转blob、file
 * getPushContent-获取消息推送文案
 * getUserInfo-获取用户信息
 * deepClone-深拷贝
 * isSessionList-是否显示会话二级列表
 * selFolderDialog-另存为
 * openLocalFile-打开文件
 * openLocalFolder-打开文件夹
 * isRegistered-判断快捷键是否注册成功
 * showMenu-右键菜单
 * addMenu-右键追加菜单
 * getOffset-获取元素距离最顶部和最左侧距离
 * getBounding-获取元素信息
 * setFcwUser-获取房产网用户名和头像
 * getFcwInfo-返回房产网用户信息
 * getUserTel-获取用户手机号排序（去重）
 * getXLMatchedStr-正则获取小乐表情
 * getFileIcon-返回文件图标的路径
 * sortTeamMembers-群主/管理员/群成员排序
 * regReplace-正则特殊字符过滤
 * getScheduleLocalStatus-获取智能日程本地状态
 * getScheduleTime-获取日程显示时间
 * dealMsgCenterContent-处理消息平台接口
 * emitMsg-和主窗口通讯
 * uploadToQiNiu-上传七牛
 * hasContent-是否有输入框内容
 * setMsgString-设置消息体字符串
 * getStrParam-获取str中的参数
 * calcWH-计算图片显示宽高
 * getLogFileInfo-获取日志文件名
 * isFcwList-是否客户咨询列表
 * getPersons-获取人员信息
 * dealMsgField-处理消息体多余字段
 * saveSrcToLocal-保存src图片到本地
 * getImageData-获取图片大小
 * getImageQualityUrl-设置图片显示压缩路径
 * getFreeLoginUrl-跳转新系统免登录
 * closeAllWin-关闭所有窗口
 * getChildWin-获取子窗口
 * setWinMethod-设置窗口方法
 * isMainWin-判断是否为主窗口
 * admZipFile-解压文件
 * getParents-获取父元素
 * openAppUrl-跳转应用url
 * setJJSEvent-发送埋点
 * reluanchApp-结束进程重启应用
 * setLocalUpdateObj-获取/设置本地更新数据对象
 * isHalfYearAgo-判断是否超过半年
 * dispatchEvent-触发事件
 * */
import {cloneDeep} from "lodash";
import {toDataURL as QRCodeToDataURL} from "qrcode";
import {decode as iconvDecode} from "iconv-lite";
import {canvasToFile, dataURLToImage, imgQuality, fileToDataURL} from "./imgCompress";
import config from "/config.js";
import {alert, textmenu} from "@comp/ui";

const fs = remote.require("fs");
const path = remote.require("path");
const os = remote.require("os");
const cp = remote.require("child_process");
const http = remote.require("http");
const crypto = remote.require("crypto");
const mime = require("mime");
const AdmZip = remote.require("adm-zip");
const heic2any = remote.require("heic2any");
let uniqueSignInput = 0;// 批量粘贴图片偶尔会一致，新增计数

// 加密编码(目前用于登录userName和通讯录workerId)
export function enCodeJJS(s) {
  let str = (s || "").split("").reverse().join("");
  if (str.length > 1) {
    return randomString(1) + str.substring(0, 1) + randomString(2) + str.substring(1) + randomString(3);
  }
  return randomString(1) + str + randomString(2);
}

// 随机生成长度字符串
export function randomString(len) {
  let base = "abcdefghijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  let maxPos = base.length;
  let pwd = "";
  for (let i = 0; i < len; i++) {
    pwd += base.charAt(Math.floor(Math.random() * maxPos));
  }
  return pwd;
}

// 生成二维码
export async function createQrCode(param) {
  let {text, width, version} = param;
  let qrParam = {
    width: width,
    margin: 0
  }
  if (version) {
    qrParam.version = version;
  }
  return QRCodeToDataURL(text, qrParam);
}

// 返回iconv.decode结果
export function getIconvDecode(buff, encoding) {
  return iconvDecode(buff, encoding);
}

// 获取网络连接电脑信息
export function getNetConnectInfo() {
  // 主窗口存在则直接返回
  if (remote.store.state.netComputerInfo.ComputerId) {
    return new Promise(resolve => {
      resolve(remote.store.state.netComputerInfo);
    });
  }
  let encoding = "gbk";
  let binaryEncoding = "binary";
  let netComputerInfo = {};
  return new Promise(resolve => {
    let diskid32Path = getAppPath(`\\Snipaste\\diskid32.exe`);
    cp.exec(`"${diskid32Path}"`, {encoding: binaryEncoding}, function (err, stdout, stderr) {
      let data = getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding);
      let dataList = data.trim().replace(/\r\n/g, "~~").split("~~");
      for (let i = 0; i < dataList.length; i++) {
        if (/Hard Drive Serial Number/.test(dataList[i])) {
          netComputerInfo.SerialNumber = dataList[i].split(":")[1].trim();
        } else if (/Hard Drive Model Number/.test(dataList[i])) {
          netComputerInfo.Model = dataList[i].split(":")[1].trim();
        } else if (/Computer ID/.test(dataList[i])) {
          netComputerInfo.ComputerId = dataList[i].split(":")[1].trim();
        } else if (/MAC Address/.test(dataList[i])) {
          if (!netComputerInfo.Mac) {
            netComputerInfo.Mac = [];
          }
          netComputerInfo.Mac.push(dataList[i].split(":")[1].trim().replace(/-/g, ""));
        }
      }
      resolve(netComputerInfo);
    });
  });
}

// 获取全部电脑信息(异步)
export function getAllComputerInfo() {
  // 主窗口存在则直接返回
  if (remote.store.state.computerInfo.macAddress) {
    return new Promise(resolve => {
      resolve(remote.store.state.computerInfo);
    });
  } else if (remote.store.state.computerInfo.promise) {
    // 避免多次同时请求
    return remote.store.state.computerInfo.promise;
  }
  try {
    let encoding = "gbk";
    let binaryEncoding = "binary";
    let data = "";//子线程返回的数据
    let dataList = [];//多个信息的数组
    let dataInfo = [];//信息提炼数组对象
    let cmdStr = "";
    let systemInfoData = {
      userUUIDMap: {}
    };
    let execParam = {encoding: binaryEncoding};
    remote.store.state.computerInfo.promise = new Promise(async resolve => {
      if (!remote.wmic) {
        await getWMIC();
      }

      let p1 = new Promise(function (resolve) {
        cmdStr = remote.wmicFlag ? `${remote.wmic} logicaldisk where "DriveType = 3" get DeviceID,Size,FreeSpace` : `Get-CimInstance -ClassName Win32_LogicalDisk -Filter "DriveType=3" | Select-Object DeviceID,Size,FreeSpace`;
        if (remote.wmicFlag) {
          delete execParam.shell;
        } else {
          execParam.shell = "powershell.exe";
        }
        cp.exec(cmdStr, execParam, function (err, stdout, stderr) {
          data = getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding);
          dataList = data.trim().replace(/\s+/g, "~~").split("~~");
          dataInfo = [];
          let startI = remote.wmicFlag ? 3 : 6;
          for (let i = startI; i < dataList.length; i = i + 3) {
            dataInfo[i / 3 - 1] = {};
            let volumeLabel = dataList[i];
            dataInfo[i / 3 - 1][dataList[0]] = volumeLabel;//设备盘符
            dataInfo[i / 3 - 1][dataList[1]] = dealMem(dataList[i + 1]);//剩余空间
            dataInfo[i / 3 - 1][dataList[1] + "Num"] = dataList[i + 1];//剩余空间
            dataInfo[i / 3 - 1][dataList[2]] = dealMem(dataList[i + 2]);//磁盘大小
            dataInfo[i / 3 - 1][dataList[2] + "Num"] = dataList[i + 2];//磁盘大小
            try {
              // 对应盘写入uuid文件
              let uuidPath = `${dataList[i]}\\$SYSTEM.BIN`;
              systemInfoData.userUUIDMap["mu-" + volumeLabel] = mkdirFileUUIDSync(uuidPath, "localhost_c.value");
              cp.exec(`attrib +h +s ${uuidPath}`);
            } catch (e) {
              console.log("uuidPathErr", e);
            }
          }
          systemInfoData.diskInfo = trimArray(dataInfo);
          resolve();
        });
      });
      let p2 = new Promise(function (resolve) {
        if (remote.wmicFlag) {
          delete execParam.shell;
          cmdStr = `ver & ${remote.wmic} path win32_operatingsystem get BuildNumber,Caption,OSArchitecture`;
          cp.exec(cmdStr, execParam, function (err, stdout, stderr) {
            data = getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding);
            dataList = data.trim().replace(/\s+\s+/g, "~~").split("~~");
            systemInfoData.VER = dataList[0];
            systemInfoData.BuildNumber = remote.wmicFlag ? dataList[4] : dataList[7];
            systemInfoData.Caption = remote.wmicFlag ? dataList[5] : dataList[8];
            systemInfoData.OSArchitecture = remote.wmicFlag ? dataList[6] : dataList[9];

            // cpu和内存
            systemInfoData.CpuModel = os.cpus()[0].model;
            systemInfoData.RamTotal = dealMem(os.totalmem());//内存总空间
            systemInfoData.RamFree = dealMem(os.freemem());//内存空闲空间

            resolve();
          });
        } else {
          cmdStr = `ver`;
          delete execParam.shell;
          let p2_1 = new Promise(function (resolve1) {
            cp.exec(cmdStr, execParam, function (err, stdout, stderr) {
              data = getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding);
              dataList = data.trim().replace(/\s+\s+/g, "~~").split("~~");
              systemInfoData.VER = dataList[0];
              resolve1();
            });
          });
          cmdStr = `Get-CimInstance -ClassName Win32_OperatingSystem | Select-Object BuildNumber,Caption,OSArchitecture,SerialNumber,zxp`;
          execParam.shell = "powershell.exe";
          let p2_2 = new Promise(function (resolve1) {
            cp.exec(cmdStr, execParam, function (err, stdout, stderr) {
              data = getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding);
              dataList = data.trim().replace(/\n/g, "~~").split("~~");
              systemInfoData.BuildNumber = getIndexStr(dataList[0], ":");
              systemInfoData.Caption = getIndexStr(dataList[1], ":");
              systemInfoData.OSArchitecture = getIndexStr(dataList[2], ":");
              systemInfoData.osSerialnumber = getIndexStr(dataList[3], ":");

              // cpu和内存
              systemInfoData.CpuModel = os.cpus()[0].model;
              systemInfoData.RamTotal = dealMem(os.totalmem());//内存总空间
              systemInfoData.RamFree = dealMem(os.freemem());//内存空闲空间
              resolve1();
            });
          });

          Promise.all([p2_1, p2_2]).then(function () {
            resolve();
          });
        }
      });

      let p3 = new Promise(function (resolve) {
        if (remote.wmicFlag) {
          delete execParam.shell;
          cmdStr = `${remote.wmic} bios get serialnumber /value`;// BIOS序列号
          cmdStr += `& ${remote.wmic} os get SerialNumber /value`;// 产品ID
          cmdStr += `& ${remote.wmic} csproduct get Name,UUID /value`;// 设备UUID
          cmdStr += `& ${remote.wmic} baseboard get product,serialnumber /value`;// 主板序列号和型号
          cmdStr += `& ${remote.wmic} cpu get Caption,processorid /value`;// cpu序列号和名称
          cmdStr += `& ${remote.wmic} diskdrive get Caption,serialnumber /value`;// 硬盘序列号和名称
          cp.exec(cmdStr, execParam, function (err, stdout, stderr) {
            try {
              data = getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding);
              dataList = data.trim().replace(/\n/g, "~~").split("~~");
              trimArray(dataList);
              systemInfoData.biosSerialnumber = getIndexStr(dataList[0], "=");
              systemInfoData.osSerialnumber = getIndexStr(dataList[1], "=");
              systemInfoData.Name = getIndexStr(dataList[2], "=");
              systemInfoData.UUID = getIndexStr(dataList[3], "=");
              systemInfoData.baseboardProduct = getIndexStr(dataList[4], "=");
              systemInfoData.baseboardSerialnumber = getIndexStr(dataList[5], "=");
              systemInfoData.cpuCaption = getIndexStr(dataList[6], "=");
              systemInfoData.cpuProcessorid = getIndexStr(dataList[7], "=");
              systemInfoData.SerialNumber = getIndexStr(dataList[9], "=");
              systemInfoData.diskdriveSerialnumber = "";
              systemInfoData.diskdriveCaption = "";
              for (let i = 8; i < dataList.length; i += 2) {
                systemInfoData.diskdriveCaption += getIndexStr(dataList[i], "=") + ",";
                systemInfoData.diskdriveSerialnumber += getIndexStr(dataList[i + 1], "=") + ",";
              }
            } catch (e) {
              console.log("cmdStrErr", e);
            }
            resolve();
          });
        } else {
          execParam.shell = "powershell.exe";
          let p3_1 = new Promise(function (resolve1) {
            cmdStr = `Get-CimInstance -ClassName Win32_BIOS -Property 'SerialNumber' | Select-Object -ExpandProperty 'SerialNumber'`;
            cp.exec(cmdStr, execParam, function (err, stdout, stderr) {
              try {
                systemInfoData.biosSerialnumber = (getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding) || "").trim();
              } catch (e) {
                console.log("cmdStrErr", e);
              }
              resolve1();
            });
          });
          let p3_2 = new Promise(function (resolve1) {
            cmdStr = `Get-CimInstance Win32_ComputerSystemProduct | Select-Object Name,UUID,zxp,zxp1,zxp2`;
            cp.exec(cmdStr, execParam, function (err, stdout, stderr) {
              try {
                data = getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding);
                dataList = data.trim().replace(/\n/g, "~~").split("~~");
                systemInfoData.Name = getIndexStr(dataList[0], ":");
                systemInfoData.UUID = getIndexStr(dataList[1], ":");
              } catch (e) {
                console.log("cmdStrErr", e);
              }
              resolve1();
            });
          });
          let p3_3 = new Promise(function (resolve1) {
            cmdStr = `Get-CimInstance -ClassName Win32_BaseBoard | Select-Object Product,SerialNumber,zxp,zxp1,zxp2`;
            cp.exec(cmdStr, execParam, function (err, stdout, stderr) {
              try {
                data = getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding);
                dataList = data.trim().replace(/\n/g, "~~").split("~~");
                systemInfoData.baseboardProduct = getIndexStr(dataList[0], ":");
                systemInfoData.baseboardSerialnumber = getIndexStr(dataList[1], ":");
              } catch (e) {
                console.log("cmdStrErr", e);
              }
              resolve1();
            });
          });
          let p3_4 = new Promise(function (resolve1) {
            cmdStr = `Get-CimInstance -ClassName Win32_Processor | Select-Object  Caption,ProcessorId,zxp,zxp1,zxp2`;
            cp.exec(cmdStr, execParam, function (err, stdout, stderr) {
              try {
                data = getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding);
                dataList = data.trim().replace(/\n/g, "~~").split("~~");
                systemInfoData.cpuCaption = getIndexStr(dataList[0], ":");
                systemInfoData.cpuProcessorid = getIndexStr(dataList[1], ":");
              } catch (e) {
                console.log("cmdStrErr", e);
              }
              resolve1();
            });
          });
          let p3_5 = new Promise(function (resolve1) {
            cmdStr = `Get-CimInstance -ClassName Win32_DiskDrive | Select-Object Caption,SerialNumber,zxp,zxp1,zxp2`;
            cp.exec(cmdStr, execParam, function (err, stdout, stderr) {
              try {
                data = getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding);
                dataList = data.trim().replace(/\n/g, "~~").split("~~");
                systemInfoData.diskdriveSerialnumber = "";
                systemInfoData.diskdriveCaption = "";
                systemInfoData.SerialNumber = getIndexStr(dataList[1], ":");
                for (let i = 0; i < dataList.length; i += 6) {
                  systemInfoData.diskdriveCaption += getIndexStr(dataList[i], ":") + ",";
                  systemInfoData.diskdriveSerialnumber += getIndexStr(dataList[i + 1], ":") + ",";
                }
              } catch (e) {
                console.log("cmdStrErr", e);
              }
              resolve1();
            });
          });

          Promise.all([p3_1, p3_2, p3_3, p3_4, p3_5]).then(function () {
            resolve();
          });
        }
      });

      let p4 = new Promise(function (resolve) {
        if (remote.wmicFlag) {
          delete execParam.shell;
          cp.exec(`${remote.wmic} nic get name,macaddress,netconnectionstatus,physicalAdapter /value`, execParam, function (err, stdout, stderr) {
            data = getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding);
            dataList = data.trim().replace(/\n/g, "~~").split("~~");
            trimArray(dataList);
            systemInfoData.Mac = "";
            systemInfoData.formatMac = "";
            systemInfoData.macName = "";
            systemInfoData.macStatus = "";
            for (let i = 0; i < dataList.length; i += 4) {
              let isPhysicalAdapter = (getIndexStr(dataList[i + 3], "=") || "").toLowerCase() == "true";
              let thisMac = getIndexStr(dataList[i], "=");
              if (isPhysicalAdapter && thisMac) {
                systemInfoData.Mac += thisMac.replace(/:/g, "") + ",";
                systemInfoData.formatMac += thisMac.replace(/:/g, "-") + ",";
                systemInfoData.macName += getIndexStr(dataList[i + 1], "=") + ",";
                systemInfoData.macStatus += getIndexStr(dataList[i + 2], "=") + ",";
              }
            }
            resolve();
          });
        } else {
          execParam.shell = "powershell.exe";
          cp.exec(`Get-CimInstance -ClassName Win32_NetworkAdapter | Select-Object macaddress,name,netconnectionstatus,physicalAdapter,zxp`, execParam, function (err, stdout, stderr) {
            try {
              data = getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding);
              dataList = data.trim().replace(/\n/g, "~~").split("~~");
              systemInfoData.Mac = "";
              systemInfoData.formatMac = "";
              systemInfoData.macName = "";
              systemInfoData.macStatus = "";
              for (let i = 0; i < dataList.length; i += 6) {
                let isPhysicalAdapter = (getIndexStr(dataList[i + 3], ":") || "").toLowerCase() == "true";
                let thisMac = getIndexStr(dataList[i], ":");
                if (isPhysicalAdapter && thisMac) {
                  systemInfoData.Mac += thisMac.replace(/:/g, "") + ",";
                  systemInfoData.formatMac += thisMac.replace(/:/g, "-") + ",";
                  systemInfoData.macName += getIndexStr(dataList[i + 1], ":") + ",";
                  systemInfoData.macStatus += getIndexStr(dataList[i + 2], ":") + ",";
                }
              }
            } catch (e) {
              console.log("cmdStrErr", e);
            }
            resolve();
          });
        }
      });

      let p5 = new Promise(function (resolve) {
        // 获取和写入本地uuid
        systemInfoData.userUUID = mkdirFileUUIDSync(getAppPath(), "localhost_c.value");
        systemInfoData.userUUIDMap.mu0 = mkdirFileUUIDSync(remote.process.env.LOCALAPPDATA + "\\" + remote.store.state.config.name, "localhost_c.value");
        resolve();
      });

      let p6 = new Promise(function (resolve) {
        // 网关mac
        cp.exec(`ipconfig`, {encoding: binaryEncoding}, function (err, stdout, stderr) {
          data = getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding);
          dataList = data.trim().replace(/\n/g, "~~").split("~~");
          let gatewayName = "";
          let gatewayMap = {};
          let gatewayIpList = [];
          let gatewayMacList = [];
          let gatewayFlag = false;
          // 获取网关ip
          for (let i = 0; i < dataList.length; i++) {
            let dataItem = (dataList[i] || "").trim();
            let nameIndex = dataItem.indexOf("适配器 ");
            if (nameIndex != -1) {
              gatewayName = dataItem.slice(nameIndex + 4, -1);
            }
            if (/默认网关/.test(dataItem)) {
              let gatewayI = (dataItem.split(":")[1] || "").trim();
              if (gatewayI) {
                gatewayMap[gatewayI] = {
                  name: gatewayName,
                };
                gatewayFlag = true;
              }
            }
          }
          if (gatewayFlag) {
            // 获取网关ip对应mac
            let cpList = [encrypt("a"), encrypt("p"), encrypt("x"), encrypt("r")];
            cp.exec(`${decrypt(cpList[0]) + decrypt(cpList[3]) + decrypt(cpList[1])} -${decrypt(cpList[0])}`, {encoding: binaryEncoding}, function (err, stdout, stderr) {
              data = getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding);
              dataList = data.trim().replace(/\n/g, "~~").split("~~");
              for (let i = 0; i < dataList.length; i++) {
                let gatewayList = trimArray((dataList[i] || "").trim().replace(/\s/g, "~~").split("~~"));
                // 只收集.1结尾的网关数据
                if (/\.1$/.test(gatewayList[0]) && gatewayMap[gatewayList[0]]) {
                  gatewayMap[gatewayList[0]].mac = gatewayList[1];
                  gatewayIpList.push(gatewayList[0]);
                  gatewayMacList.push(gatewayList[1].replace(/-/g, "").toUpperCase());
                }
              }
              systemInfoData.gatewayMap = gatewayMap;
              systemInfoData.gatewayIp = gatewayIpList.join(",");
              systemInfoData.gatewayMac = gatewayMacList.join(",");
              resolve();
            });
          } else {
            systemInfoData.gatewayMap = gatewayMap;
            resolve();
          }
        });
      });

      Promise.all([p1, p2, p3, p4, p5, p6]).then(function () {
        // 合并所有mac(历史逻辑)
        let interfacesData = getBaseComputerInfo();
        let allMacStr = interfacesData.macStr;
        if (systemInfoData.SerialNumber) {
          allMacStr += "," + systemInfoData.SerialNumber;
        }
        if (systemInfoData.Mac) {
          allMacStr += "," + systemInfoData.Mac;
        }
        systemInfoData.macAddress = [...new Set(allMacStr.split(","))].join(",");
        console.log("computerInfo:", encryptRsa(JSON.stringify(interfacesData), "log"));
        console.log("systemInfoData:", encryptRsa(JSON.stringify(systemInfoData), "log"));
        resolve(systemInfoData);
        delete remote.store.state.computerInfo.promise;
      }).catch(function (error) {
        console.log("getSystemDataAllErr", error);
        resolve(systemInfoData);
        delete remote.store.state.computerInfo.promise;
      });
    });
    return remote.store.state.computerInfo.promise;
  } catch (e) {
    console.log("getSystemDataErr", e);
    return new Promise(function (resolve) {
      resolve({});
    });
  }
}

// 判断wmic是否可用，不可用改变调用位置
export function getWMIC() {
  let encoding = "gbk";
  let binaryEncoding = "binary";
  let data = "";//子线程返回的数据
  return new Promise(resolve => {
    cp.exec(`wmic diskdrive get Caption,serialnumber`, {encoding: binaryEncoding}, function (err, stdout, stderr) {
      data = getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding);
      if (/'wmic' 不是内部或外部命令/.test(data)) {
        if (!fs.existsSync(remote.process.env.SystemRoot + "//System32//wbem//WMIC.exe")) {
          cp.exec(`where powerShell`, {encoding: binaryEncoding}, function (err, stdout, stderr) {
            data = (getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding) || "").trim();
            remote.wmic = data + " ";
            remote.wmicFlag = false;
            resolve();
          });
        } else {
          remote.wmic = remote.process.env.SystemRoot + "//System32//wbem//WMIC.exe ";
          remote.wmicFlag = true;
          resolve();
        }
      } else {
        remote.wmic = "wmic ";
        remote.wmicFlag = true;
        resolve();
      }
    });
  });
}

// 获取第一个出现字符串的下标到最后的字符串
export function getIndexStr(str, key) {
  str = str || "";
  return (str.substring(str.indexOf(key) + 1) || "").trim();
}

// 去除一维数组空值
export function trimArray(list) {
  // 去除空格
  for (let i = 0; i < list.length; i++) {
    if (list[i] == null || String(list[i]).trim() == "") {
      list.splice(i, 1);
      i--;
    }
  }
  return list;
}

// 获取电脑基本信息（同步）
export function getBaseComputerInfo() {
  try {
    let interfaces = os.networkInterfaces();
    let interfacesData = {
      sysVer: os.release() || "",
      macStr: "",
    }
    for (let devName in interfaces) {
      let iface = interfaces[devName];
      for (let i = 0; i < iface.length; i++) {
        let alias = iface[i];
        if ((alias.family == 4 || alias.family == "IPv4") && alias.address != "127.0.0.1" && !alias.internal && !interfacesData.address) {
          interfacesData.address = alias.address;
        }
        if ((alias.family == 4 || alias.family == "IPv4") && alias.address != "127.0.0.1" && !alias.internal && alias.mac != "00:00:00:00:00:00") {
          if (!interfacesData.hostName) {
            interfacesData.hostName = "pcMac-" + alias.mac.split(":").join("").toUpperCase();
          }
          /*虚拟机网卡不记录下来*/
          if (alias.mac != "00-50-56-C0-00-08") {
            interfacesData.macStr += alias.mac.split(":").join("").toUpperCase() + ",";
          }
        }
      }
    }
    interfacesData.macStr = interfacesData.macStr.slice(0, -1);
    global.interfacesData = interfacesData;
    return interfacesData;
  } catch (e) {
    console.log("getBaseComputerInfoErr", e);
    // 当前版本的os.networkInterfaces()会报错
    return global.interfacesData;
  }
}

// 获取当前cpu信息
export function getCpus() {
  let cpus = os.cpus();
  let user = 0, nice = 0, sys = 0, idle = 0, irq = 0, total = 0;
  for (let cpu in cpus) {
    let times = cpus[cpu].times;
    user += times.user;
    nice += times.nice;
    sys += times.sys;
    idle += times.idle;
    irq += times.irq;
  }
  total += user + nice + sys + idle + irq;
  return {idle: idle, total: total};
}

// 内存单位转换
export function dealMem(bytes, point = 2) {
  if (bytes == 0) return "0 B";
  let k = 1024, // or 1024
    sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"],
    i = Math.floor(Math.log(bytes) / Math.log(k));
  let numberStr = (bytes / Math.pow(k, i));
  let pointNum = Math.pow(10, point);
  return Math.floor(numberStr * pointNum) / pointNum + " " + sizes[i];
}

// 获取当前安装列表和运行列表
export function getExeList() {
  try {
    let encoding = "gbk";
    let binaryEncoding = "binary";
    let data = "";//子线程返回的数据
    let dataList = [];//多个信息的数组
    let dataInfo = [];//信息提炼数组对象
    let exeInfoData = {
      installInfo: {},
      launchInfo: {}
    };
    let p1 = new Promise(function (resolve) {
      cp.exec(`tasklist /fo list`, {encoding: binaryEncoding}, function (err, stdout, stderr) {
        data = getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding);
        dataList = data.trim().replace(/\n/g, "~~").split("~~");
        dataInfo = [];
        for (let i = 0; i < dataList.length; i += 6) {
          if (getIndexStr(dataList[i + 2], ":").trim() == "Console") {
            let appName = getIndexStr(dataList[i], ":").trim();
            exeInfoData.launchInfo[appName] = appName;
          }
        }
        resolve();
      });
    });
    return new Promise(function (resolve) {
      Promise.all([p1]).then(function () {
        console.log("getExeList", encrypt(JSON.stringify(exeInfoData)));
        resolve(exeInfoData);
      }).catch(function (error) {
        console.log("getExeListErr", error);
        resolve({});
      });
    });
  } catch (e) {
    console.log("getExeListErr", e);
    return new Promise(function (resolve) {
      resolve({});
    });
  }
}

// 防抖
let debounceTimer = {};

export function debounce(param) {
  let {timerName, fnName, time, e} = param;
  if (e) {
    let code = e.keyCode;
    if (code == 27 || code == 13 || code == 37 || code == 38 || code == 39 || code == 40 || code == 9) {
      return;
    }
  }
  if (debounceTimer[timerName]) {
    clearTimeout(debounceTimer[timerName]);
    delete debounceTimer[timerName];
  }
  debounceTimer[timerName] = setTimeout(() => {
    delete debounceTimer[timerName];
    fnName()
  }, time);
  return debounceTimer[timerName];
}

// 节流
let throttleTimer = {};

export function throttle(param) {
  let {timerName, fnName, time} = param;
  if (throttleTimer[timerName]) {
    return;
  }
  throttleTimer[timerName] = setTimeout(() => {
    delete throttleTimer[timerName];
    fnName()
  }, time);
}

// 时间格式化 用法yyyy-MM-dd可自定义 dateFormat(new Date(), "yyyy-MM-dd")
export const dateFormat = (function () {
  let _map = {i: !0, r: /\byyyy|yy|MM|cM|eM|M|dd|d|HH|H|mm|ms|ss|m|s|w|ct|et\b/g},
    _12cc = ["上午", "下午"],
    _12ec = ["A.M.", "P.M."],
    _week = ["日", "一", "二", "三", "四", "五", "六"],
    _cmon = ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十", "十一", "十二"],
    _emon = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sept", "Oct", "Nov", "Dec"];
  let _fmtnmb = function (_number) {
    _number = parseInt(_number) || 0;
    return (_number < 10 ? "0" : "") + _number;
  };
  let _fmtclc = function (_hour) {

    return _hour < 12 ? 0 : 1;
  };
  return function (_time, _format, _12time) {
    if (!_time || !_format)
      return "";
    _time = new Date(_time);
    _map.yyyy = _time.getFullYear();
    _map.yy = ("" + _map.yyyy).substr(2);
    _map.M = _time.getMonth() + 1;
    _map.MM = _fmtnmb(_map.M);
    _map.eM = _emon[_map.M - 1];
    _map.cM = _cmon[_map.M - 1];
    _map.d = _time.getDate();
    _map.dd = _fmtnmb(_map.d);
    _map.H = _time.getHours();
    _map.HH = _fmtnmb(_map.H);
    _map.m = _time.getMinutes();
    _map.mm = _fmtnmb(_map.m);
    _map.s = _time.getSeconds();
    _map.ss = _fmtnmb(_map.s);
    _map.ms = _time.getMilliseconds();
    _map.w = _week[_time.getDay()];
    let _cc = _fmtclc(_map.H);
    _map.ct = _12cc[_cc];
    _map.et = _12ec[_cc];
    if (!!_12time) {
      _map.H = _map.H % 12;
    }

    _format = "" + _format;
    if (!_map || !_format) {
      return _format || "";
    }
    return _format.replace(_map.r, function ($1) {
      let _result = _map[!_map.i ? $1.toLowerCase() : $1];
      return _result != null ? _result : $1;
    });
  };
})();

// 格式化显示时间 time时间戳
export const transTime = (function () {
  let getDayPoint = function (time) {
    time.setMinutes(0);
    time.setSeconds(0);
    time.setMilliseconds(0);
    time.setHours(0);
    let today = time.getTime();
    time.setMonth(1);
    time.setDate(1);
    let yearDay = time.getTime();
    return [today, yearDay];
  };
  return function (time, type) {
    // type默认列表时间节点-1为消息时间节点
    let check = getDayPoint(new Date());
    if (time >= check[0]) {
      return dateFormat(time, "HH:mm")
    } else if (time >= check[0] - 60 * 1000 * 60 * 24) {
      return `昨天${type == 1 ? dateFormat(time, " HH:mm") : ""}`;
    } else if (time >= (check[0] - 2 * 60 * 1000 * 60 * 24)) {
      return `前天${type == 1 ? dateFormat(time, " HH:mm") : ""}`;
    } else if (time >= (check[0] - 7 * 60 * 1000 * 60 * 24)) {
      return dateFormat(time, `MM-dd${type == 1 ? " HH:mm" : ""}`)
    } else if (time >= check[1]) {
      return dateFormat(time, `MM-dd${type == 1 ? " HH:mm" : ""}`)
    } else {
      return dateFormat(time, `yyyy-MM-dd${type == 1 ? " HH:mm" : ""}`)
    }
  }
})();

//文件size转大小
export function getFileSize(size) {
  if (size === 0) return '0 B';
  let k = 1024,
    sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],
    i = Math.floor(Math.log(size) / Math.log(k));

  return (size / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i];
}

// 获取文件后缀
export function getFileExt(name = "", type) {
  let index = name.lastIndexOf(".");
  return index == -1 ? (type == 1 ? "" : "unknown") : name.slice(index + 1);
}

// 获取base64图片后缀
export function getBase64Ext(base64, type) {
  return type == 1 ? mime.getExtension(base64) : mime.getExtension(base64.split(";base64,")[0].slice(5));
}

// 下载文件 name文件名(带后缀) path文件路径(最后带\) ext文件后缀 md5文件唯一标识 size文件大小 url文件远程地址 done回调 replaceFile不替换文件名下载
export function downloadFile(param) {
  let fileUrlObj = "";
  try {
    fileUrlObj = new URL(param.url);
  } catch (error) {
    param.state = "error";
    param.msg = "下载地址错误";
    param.done(param);
    console.log("downloadFileErr-url:", error);
    return;
  }
  try {
    mkdirsSync(param.path);
    let filePath = param.path;
    // 去除后缀的点
    if (/\./.test(param.ext)) {
      param.ext = param.ext.split(".")[1];
    }
    // 下载名不存在后缀自动带上
    if (param.ext && !new RegExp(`.${param.ext}`).test(param.name)) {
      param.name += `.${param.ext}`;
    }
    let fileName = removeReplace(param.replaceFile ? param.name : existFileName(param));
    param.name = fileName;
    let fileLocalUrl = filePath + fileName;
    let httpOptions = {
      host: fileUrlObj.host,
      port: fileUrlObj.port,
      path: fileUrlObj.pathname + fileUrlObj.search,
    }
    // 替换文件删除原文件
    if (param.replaceFile) {
      let flag = unlinkFile(fileLocalUrl, param);
      if (!flag) {
        return;
      }
    }
    let file = fs.createWriteStream(fileLocalUrl);
    let clientRequest = http.get(httpOptions, function (res) {
      param.destroy = res.destroy;
      // 关闭
      res.on("close", function (e) {
        try {
          if (clientRequest.timeoutTimer) {
            clearTimeout(clientRequest.timeoutTimer);
          }
        } catch (e) {}
        // 资源下载完毕不清理
        if (!param.isEnd) {
          // 触发错误方法不关闭
          if (param.state != "error") {
            param.state = "close";
            param.msg = "关闭";
            file.end();
            unlinkFile(fileLocalUrl);
            param.done(param);
          }
          console.log(`downloadFileErr-close-${param.state}-${param.msg}`);
        }
      });
      if (res.statusCode != 200) {
        res.destroy();
        param.state = "error";
        param.msg = res.statusMessage;
        file.end();
        unlinkFile(fileLocalUrl);
        param.done(param);
        console.log("downloadFileStatus", res.statusCode, res.statusMessage);
        return;
      }
      res.on("data", function (data) {
        // 没有写完，暂停读取流
        if (file.write(data, function (err) {
          if (err) {
            try {
              if (clientRequest.timeoutTimer) {
                clearTimeout(clientRequest.timeoutTimer);
              }
            } catch (e) {}
            console.log("httpWriteErr", err);
            param.state = "error";
            if (!param.code) {
              param.msg = httpErrorMsg(err);
              param.code = err.code;
            }
            file.end();
            unlinkFile(fileLocalUrl);
            param.done(param);
          }
        }) === false) {
          res.pause();
        }
        if (param.state == "error") {
          return;
        }
        let total = res.headers["content-length"] || param.size || 0;
        param.state = "process";
        param.percentage = (Math.round(file.bytesWritten / total * 10000) / 100).toFixed(2);
        param.alreadySize = file.bytesWritten;
        param.total = total;
        if (!param.consolePer) {
          param.consolePer = 0;
        }
        httpTimeout(clientRequest, param, file, fileLocalUrl);
        //返回百分比
        param.done(param);
      });
      // 写完后，继续读取
      file.on("drain", function () {
        if (param.state != "error") {
          res.resume();
        }
      });
      // 文件错误
      file.on("error", function (error) {
        res.resume();
        param.state = "error";
        param.msg = httpErrorMsg(error);
        param.code = error.code;
        param.done(param);
        console.log("downloadFileErr-file:", error);
      });
      // 下载完成
      res.on("end", function () {
        param.isEnd = true;
        let fileTotal = res.headers["content-length"] || param.size || 0;
        file.end();

        setTimeout(() => {
          // 判断下载的文件是否是一样的大小，如果差别过大，直接认为是失败的。
          fs.stat(fileLocalUrl, function (error, info) {
            if (error) {
              param.msg = error.message;
              param.state = "error";
              param.code = error.code;
            } else {
              // 相差5byte认为是下载错误的
              if (fileTotal - info.size > 5) {
                param.state = "error";
                unlinkFile(fileLocalUrl)
              } else {
                param.state = "success";
              }
            }
            param.returnPercentage = 100;
            param.done(param);
          });
        }, 100);
      });
    }).on("error", function (error) {
      if (!clientRequest.timeoutFlag) {
        if (error.code == "ECONNRESET" && error.message == "socket hang up") {
          param.state = "close";
          param.msg = "关闭";
        } else {
          param.state = "error";
          param.msg = httpErrorMsg(error);
        }
        param.code = error.code;
        file.end();
        unlinkFile(fileLocalUrl)
        try {
          if (clientRequest.timeoutTimer) {
            clearTimeout(clientRequest.timeoutTimer);
          }
        } catch (e) {}
        param.done(param);
      }
      console.log("downloadFileErr-error", clientRequest.timeoutFlag, error);
    });
    param.clientRequest = clientRequest;
    param.percentage = "0.00";
    // 断网的时候error比start早触发
    if (param.state != "error") {
      param.state = "start";
      param.done(param);
      param.state = "process";
    }
    httpTimeout(clientRequest, param, file, fileLocalUrl);
    // 缓存图片不打印日志
    if (!config.imgTypeReg.test(param.ext)) {
      console.log("downloadFile", param);
    }
    param.done(param);
  } catch (error) {
    param.state = "error";
    param.msg = httpErrorMsg(error);
    param.code = error.code;
    console.log("downloadFileErr-catch:", error);
    param.done(param);
  }
}

// 删除文件
export function unlinkFile(fileLocalUrl, param) {
  try {
    if (fs.existsSync(fileLocalUrl)) {
      fs.unlinkSync(fileLocalUrl);
    }
    return true;
  } catch (e) {
    console.log("fileEndErr", e);
    if (param) {
      param.state = "error";
      param.msg = e.code == "EBUSY" ? `文件${e.path}被占用，请关闭对应程序后重试` : e.msg;
      param.code = e.code;
      param.done(param);
    }
    return false;
  }
}

// 删除目录和目录下的文件
export function unlinkDir(dir) {
  try {
    let files = fs.readdirSync(dir);
    files.forEach(function (file) {
      let stats = fs.statSync(`${dir}/${file}`);
      if (stats.isDirectory()) {
        unlinkDir(`${dir}/${file}`);
      } else {
        fs.unlinkSync(`${dir}/${file}`);
      }
    });
    fs.rmdirSync(dir);
  } catch (error) {
    console.error("unlinkDir", error);
  }
}

// 下载错误提示
function httpErrorMsg(err) {
  let msg = err.msg
  switch (err.code) {
    case "ENOSPC":
      msg = `您的电脑${getFileCachedPath({type: 4}).split(":")[0]}盘内存不足，请清理硬盘数据或到设置-通用设置-文件管理修改路径`;
      break;
    case "ERR_STREAM_DESTROYED":
      msg = `文件被占用`;
      break;
    case "ENOTFOUND":
      msg = `网络异常：网络不可用，请检查你的网络设置`;
      if (window.navigator.onLine) {
        console.log("httpError-online", err);
      }
      break;
  }
  return msg;
}

// 下载超时方法
function httpTimeout(clientRequest, param, file, fileLocalUrl) {
  try {
    if (clientRequest.timeoutTimer) {
      clearTimeout(clientRequest.timeoutTimer);
    }
    // 30s超时
    clientRequest.timeoutFlag = false;
    clientRequest.timeoutTimer = setTimeout(() => {
      param.state = "error";
      param.msg = "网络异常：网络不可用，请检查你的网络设置";
      param.code = "TIMEOUT";
      param.done(param);
      file.end();
      unlinkFile(fileLocalUrl)
      clientRequest.timeoutTimer = "";
      clientRequest.timeoutFlag = true;
      clientRequest.destroy();
    }, 1 * 30 * 1000);
  } catch (e) {}
}

// 缓存图片和文件机制-必传参数url、ext、fileDB-url需要完整的url不要截取
export async function loadCache(param) {
  // isDownLoad是否下载、reDownLoad是否重新下载、replaceFile是否替换文件、filePath指定下载路径、fileName指定文件名、getSuccess只返回下载成功的进度、done回调
  let {url, size, ext, fileDB, isDownLoad, reDownLoad, replaceFile, filePath, fileName, getSuccess, jjsPicUrlOrigin, done} = param;
  // 不存在必传参数 或 不是http、https域名的图片返回地址
  if (!fileDB || !/https?:\/\//.test(url)) {
    return {url: url};
  }
  let isImage = config.imgTypeReg.test(ext);
  let thisMd5 = MD5(url);
  // 还原云信地址获取是否存在本地文件
  if (jjsPicUrlOrigin) {
    thisMd5 = MD5(url.replace(new RegExp(config.jjsPicUrlOther), jjsPicUrlOrigin));
  }
  // 查询缓存记录
  let fileCache = await fileDB.query("md5", thisMd5);
  // 本地缓存记录
  let fileInfo = fileCache && fileCache.length > 0 ? fileCache[0] : {};
  // 存在文件
  let fileStat = fileInfo.path && fs.existsSync(fileInfo.path + fileInfo.name);
  // 下载进程
  let downloadFileObj = "";
  // 存在文件且不是另存为返回本地路径
  if (fileStat && !replaceFile && !reDownLoad) {
    url = fileInfo.path + fileInfo.name;
    // 图片存在本地小图返回缩略图
    let thumUrl = path.join(getFileCachedPath({type: 2, account: getUserInfo().workerNo}), fileInfo.name);
    if (isImage && fs.existsSync(thumUrl)) {
      url = thumUrl;
    }
    fileInfo.url = url;
  } else {
    // 是否下载
    if (isDownLoad) {
      downloadFileObj = new Promise((resolve) => {
        downloadFile({
          url: url,
          size: size,
          name: fileName || (ext ? (thisMd5 + "." + ext) : thisMd5),
          path: filePath || (getFileCachedPath({type: isImage ? 1 : 3, account: getUserInfo().workerNo})),
          ext: ext,
          replaceFile: replaceFile,
          done: async (data) => {
            data.md5 = thisMd5;
            if (data.state == "success") {
              // 存在本地缓存文件不存在则删除
              if (fileInfo.path && !fileStat) {
                fileDB.del("md5", thisMd5);
                fileInfo = {};
              }
              await addFileDB(fileDB, thisMd5, data);
              // 下载图片生成缩略图
              if (isImage) {
                // 判断是否heic文件，是则转为jpg
                await heicToJpg({localSrc: data.path + data.name});
                await saveImageLocal(data);
              }
              resolve(data);
            } else {
              // 默认返回下载对象，getSuccess为只获取成功状态的
              if (!getSuccess || data.state == "error") {
                resolve(data);
              }
            }
            // 返回下载进度
            if (done) {
              done(data);
            }
          }
        })
      });
    }
  }
  return {url: url, fileInfo: fileInfo, downloadFileObj: downloadFileObj};
}

// 添加本地文件数据库
export function addFileDB(fileDB, thisMd5, data) {
  return new Promise(async resolve => {
    // 查询是否存在记录
    let fileCache = await fileDB.query("md5", thisMd5);
    let fileInfo = fileCache && fileCache.length > 0 ? fileCache[0] : {};
    if (fileInfo.path) {
      // 删除原有记录
      fileDB.del("md5", thisMd5);
    }
    // 查询是否存在同个路径的文件
    fileCache = await fileDB.query("path", data.path);
    fileInfo = fileCache && fileCache.length > 0 ? fileCache[0] : {};
    if (fileInfo.path && fileInfo.path == data.path && fileInfo.name == data.name && fileInfo.size != data.total) {
      // 文件被替换删除原有记录
      fileDB.del("path", data.path);
      remote.store.commit("setRemoveMD5Obj", {type: "add", ...fileInfo});
    }
    // 移除被替换数据
    if (remote.store.state.removeMD5Obj[thisMd5]) {
      remote.store.commit("setRemoveMD5Obj", {type: "del", md5: thisMd5});
    }
    // 下载完成保存本地
    fileDB.add({
      md5: thisMd5,
      path: data.path,
      name: data.name,
      size: data.total,
      url: data.url
    });
    resolve(true);
  })
}

// 读取本地文件获取file对象、blob对象、base64
export function getLocalFile(param) {
  let filePath = param.path + param.name;
  if (param.filePath) {
    filePath = param.filePath;
  }
  return new Promise((resolve) => {
    fs.readFile(filePath, function (err, buffer) {
      if (err) {
        console.log("getLocalBlobError", err);
        return;
      }
      let imgType = param.type || mime.getType(filePath) || "";
      if (param.blob) {
        resolve(new Blob([buffer], {type: imgType}));
      } else if (param.base64) {
        resolve(`data:${imgType};base64,${buffer.toString("base64")}`);
      } else {
        let file = new File([buffer], param.name, {type: imgType});
        file.filePath = filePath;
        resolve(file);
      }
    });
  })
}

// 保存图片至缩略图
export async function saveImageLocal(param) {
  let file = await getLocalFile(param);
  // 压缩图片
  let res = await imgQuality({file: file});
  return await saveBase64Local({path: path.join(getFileCachedPath({type: 2, account: getUserInfo().workerNo}), param.name), base64: res.base64});
}

// 保存base64图片至本地
export async function saveBase64Local(param) {
  return new Promise(resolve => {
    fs.writeFile(param.path, imgBase64ToBuffer(param.base64), function (err) {
      if (err) {
        console.log("saveBase64Local", err);
        resolve(false);
      } else {
        resolve(true);
      }
    });
  });
}

// 获取文件缓存路径 type-1图片-2压缩图片-3默认文件-4缓存目录-5根目录-6输入框图片 account-用户6位数id
export function getFileCachedPath(param = {}) {
  // 创建目录
  let appPath = localStorage.getItem("fileCachePath") || getAppPath();
  if (param.account) {
    mkdirsSync(path.join(appPath, `/cached/${param.account}/files/`));
    mkdirsSync(path.join(appPath, `/cached/${param.account}/images/thum/`));
  }
  mkdirsSync(path.join(appPath, `/cached/input/`));
  // 返回路径
  if (param.type == 5) {
    return path.join(appPath, `/cached/`);
  } else if (param.type == 6) {
    return path.join(appPath, `/cached/input/`);
  } else {
    return path.join(appPath, `/cached/${param.account}/${param.type == 1 ? "images" : param.type == 2 ? "images/thum" : param.type == 3 ? "files" : ""}/`);
  }
}

// img标签转base64
export function getBase64Image(img, ext) {
  let canvas = document.createElement("canvas");
  canvas.width = img.naturalWidth;
  canvas.height = img.naturalHeight;
  let mineType = mime.getType(ext) || "image/png";

  let ctx = canvas.getContext("2d");
  ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

  let dataURL = canvas.toDataURL(mineType);
  return dataURL;
}

// 获取app运行路径
export function getAppPath(thisPath, isPublic) {
  return remote.process.cwd() + (isPublic ? "\\public" : "") + (thisPath || "");
}

// 递归创建目录 同步方法
export function mkdirsSync(dirname) {
  if (fs.existsSync(dirname)) {
    return true;
  } else {
    if (mkdirsSync(path.dirname(dirname))) {
      fs.mkdirSync(dirname);
      return true;
    }
  }
}

// 写入指定位置uuid文件
export function mkdirFileUUIDSync(dirname, filename) {
  let fileData = "";
  try {
    mkdirsSync(dirname);
    let filePath = dirname + "\\" + filename;
    try {
      // 读取uuid不存在则写入
      if (fs.existsSync(filePath)) {
        fileData = decrypt(fs.readFileSync(filePath, "utf-8"));
      }
    } catch (e) {
      console.log("mkdirFileSync-existsSync-err", e);
    }
    // 读取失败则重新写入
    try {
      if (!fileData) {
        fileData = crypto.randomUUID();
        fs.writeFileSync(filePath, encrypt(fileData), "utf-8");
      }
    } catch (e) {
      console.log("mkdirFileSync-writeFileSync-err", e);
    }
  } catch (e) {
    console.log("mkdirFileSyncErr", e);
  }
  return fileData;
}

// 图片base64转buffer
export function imgBase64ToBuffer(base64) {
  return new Buffer(base64.replace(/^data:image\/.*?;base64,/, ""), "base64");
}

// 判断本地是否存在改名字文件
export function existFileName(param) {
  let name = removeReplace(param.name);
  let ext = "." + param.ext;
  let index = name.lastIndexOf(ext);
  name = index != -1 ? name.slice(0, index) : name;
  name = name + (param.num ? `(${param.num})` : "") + (index != -1 ? ext : "");
  if (fs.existsSync(param.path + name)) {
    if (!param.num) {
      param.num = 0;
    }
    param.num++;
    return existFileName(param);
  } else {
    return name;
  }
}

// 比较版本号
export function compareVersion(local, server) {
  let localList = local.split(".");
  let serverList = server.split(".");
  if (parseInt(localList[0]) > parseInt(serverList[0]) || (parseInt(localList[0]) == parseInt(serverList[0]) && parseInt(localList[1]) > parseInt(serverList[1])) ||
    (parseInt(localList[0]) == parseInt(serverList[0]) && parseInt(localList[1]) == parseInt(serverList[1]) && parseInt(localList[2]) > parseInt(serverList[2])) ||
    (parseInt(localList[0]) == parseInt(serverList[0]) && parseInt(localList[1]) == parseInt(serverList[1]) && parseInt(localList[2]) == parseInt(serverList[2]) && parseInt(localList[3] || 0) >= parseInt(serverList[3] || 0))) {
    return false;
  } else {
    return true;
  }
}

// MD5
export function MD5(str) {
  return crypto.createHash("md5").update(String(str)).digest("hex");
}

// aes-128-cbc 加密
export function encrypt(data, key, iv, algorithm) {
  if (key == null) {
    key = "d27a688ee3b40ec2";
  }
  if (iv == null) {
    iv = "344d009495fe76c5";
  }
  if (algorithm == null) {
    algorithm = "aes-128-cbc";
  }
  let cipher = crypto.createCipheriv(algorithm, key, iv);
  let crypted = cipher.update(data, "utf8", "binary");
  crypted += cipher.final("binary");
  crypted = new Buffer(crypted, "binary").toString("base64");
  return crypted;
}

// aes-128-cbc 解密
export function decrypt(data, key, iv, algorithm) {
  if (key == null) {
    key = "d27a688ee3b40ec2";
  }
  if (iv == null) {
    iv = "344d009495fe76c5";
  }
  if (algorithm == null) {
    algorithm = "aes-128-cbc";
  }
  let crypted = new Buffer(data, "base64").toString("binary");
  let decipher = crypto.createDecipheriv(algorithm, key, iv);
  let decoded = decipher.update(crypted, "binary", "utf8");
  decoded += decipher.final("utf8");
  return decoded;
}

// rsa公钥加密
export function encryptRsa(data, key) {
  let publicKey = `
-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDEnqcT+AKTbgcA6xBOUctRbjgs
7ck9dfUx77Yhb0DiCX+zluLQI8cHwIwghwbVNRDRW4xx3Gxnwx3aJ6UnkqlIQeKJ
OKNmCvG9y+0Lk3nOW9I1VyEMHTOzRfvG70mpp7L44Cbm4K86In5fA6NyDWb1a5xp
/trOBT6g3MKZ+Y2oPwIDAQAB
-----END PUBLIC KEY-----`;
  if (config.env != "online") {
    publicKey = `
-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCID1qv/GrjrmZzr09O35Ipn4KK
IpAMzEDh+FesU6Zy1S5Yg9aFml2qEMNz+s3UlCIGCbntV58dZS3PhFftNZdIm3Zd
8hK1KuTfSimb8pwmI/nLPdQaA8UqWQMvcEItYx7qWr1l2VX9/U6/tBpTkG+x7Bgr
Y7H+P6xoFo/vjFCbrwIDAQAB
-----END PUBLIC KEY-----`;
  }
  // 存在加密的公钥
  if (key) {
    publicKey = key;
    // 写入日志
    if (key == "log") {
      publicKey = `
-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDztXY7Df+sCUGlrKB1fr/p22NZ
dZt0iyXiEQ2SZ/XgaXlWLyEB7a0QA34Ufu4umSQwT1gqBBDAKtW4KPNE1yMEYeRV
iSQGZUWj3QtB4rWnWz8vLph6MCkk1xC6vFsmqg2csdbt9lDkHiDb+u3W95ebOsXq
+IS6jkwfjTSjA7XbUwIDAQAB
-----END PUBLIC KEY-----`
    }
  }
  const buffer = Buffer.from(data, "utf8");
  const chunkSize = 117;
  const inputLen = buffer.length;
  const chunks = [];
  let i = 0;

  // 字符串过长拆分
  while (i < inputLen) {
    const end = i + chunkSize;
    const chunk = buffer.slice(i, end);
    chunks.push(chunk);
    i = end;
  }

  // rsa加密
  const encryptedChunks = chunks.map((chunk) => {
    return crypto.publicEncrypt({
      key: publicKey,
      padding: crypto.constants.RSA_PKCS1_PADDING,
    }, chunk);
  });

  // 字符串过长合并
  let encryptedBuffer = "";
  for (let i = 0; i < encryptedChunks.length; i++) {
    if (!encryptedBuffer) {
      encryptedBuffer = Buffer.from(encryptedChunks[i]);
    } else {
      encryptedBuffer = Buffer.concat([encryptedBuffer, Buffer.from(encryptedChunks[i])])
    }
  }
  return encryptedBuffer.toString("base64");
}

// rsa私钥解密
export function decryptRsa(data, privateKey) {
  const buffer = Buffer.from(data, "base64");
  const chunkSize = 128;
  const inputLen = buffer.length;
  const chunks = [];
  let i = 0;

  // 字符串过长拆分
  while (i < inputLen) {
    const end = i + chunkSize;
    const chunk = buffer.slice(i, end);
    chunks.push(chunk);
    i = end;
  }

  // rsa解密
  const decryptedChunks = chunks.map((chunk) => {
    return crypto.privateDecrypt({
      key: privateKey,
      padding: crypto.constants.RSA_PKCS1_PADDING,
    }, chunk);
  });

  // 字符串过长合并
  let decryptedBuffer = "";
  for (let i = 0; i < decryptedChunks.length; i++) {
    if (!decryptedBuffer) {
      decryptedBuffer = Buffer.from(decryptedChunks[i]);
    } else {
      decryptedBuffer = Buffer.concat([decryptedBuffer, Buffer.from(decryptedChunks[i])])
    }
  }

  return decryptedBuffer.toString("utf-8");
}

// 解密文件
export function decryptRsaFile(filePath, privateKey) {
  // 获取加密文件内容
  let text = fs.readFileSync(filePath, "utf-8");
  let textList = text.split("\n");
  let decryptText = "";
  // 根据换行解密
  textList.map(item => {
    decryptText += decryptRsa(item, privateKey) + "\n";
  });
  // 解密后写入新文件
  let newFilePath = path.join(path.dirname(filePath), path.basename(filePath) + "_decrypt");
  fs.writeFileSync(newFilePath, decryptText, "utf-8");
}

// rsa私钥签名
export function signRsa(data, key) {
  // 使用私钥进行签名
  let sign = crypto.createSign("RSA-SHA256");
  sign.write(data);
  sign.end();
  return sign.sign(key, "base64");
}

// 生成rsa密钥对
export function renderRsa(savePrivateKey, resetFlag) {
  let localPrivateKeyPath = getAppPath(`\\localhost_c.private`);
  // 上传成功后保存私钥到本地
  if (savePrivateKey) {
    try {
      fs.writeFileSync(localPrivateKeyPath, encrypt(savePrivateKey), "utf-8");
    } catch (e) {
      console.log("privateKey-writeFileSync-err", e);
    }
    return;
  }
  // 获取本地私钥
  if (!resetFlag) {
    let localPrivateKey = "";
    try {
      if (fs.existsSync(localPrivateKeyPath)) {
        localPrivateKey = decrypt(fs.readFileSync(localPrivateKeyPath, "utf-8"));
      }
    } catch (e) {
      console.log("localPrivate-existsSync-err", e);
    }
    // 存在私钥直接返回
    if (localPrivateKey) {
      remote.privateKey = localPrivateKey;
      return {privateKey: localPrivateKey};
    }
  }
  // 不存在则，生成的私钥以 PKCS#8 格式生成，生成的公钥以 X.509 格式生成。
  let rsaObj = {};
  try {
    rsaObj = crypto.generateKeyPairSync('rsa', {
      modulusLength: 1024, // 密钥长度
      publicKeyEncoding: {
        type: 'spki', // 公钥格式
        format: 'pem' // 输出格式
      },
      privateKeyEncoding: {
        type: 'pkcs8', // 私钥格式
        format: 'pem' // 输出格式
      }
    });
  } catch (e) {
    console.log("rsaObjErr", e);
  }
  remote.privateKey = rsaObj.privateKey;
  remote.publicKey = rsaObj.publicKey;
  return {
    privateKey: rsaObj.privateKey,
    publicKey: rsaObj.publicKey,
  }
}

// 全转义HTML
export function htmlEscapeAll(text, xss) {
  if (xss) {
    return (text || "").replace(/&/g, "&amp;").replace(/[<]/g, "&lt;").replace(/[>]/g, "&gt;").replace(/\t/g, "&nbsp;&nbsp;&nbsp;&nbsp;");
  } else {
    return (text || "").replace(/&/g, "&amp;").replace(/[<]/g, "&lt;").replace(/[>]/g, "&gt;")
  }
}

// 去除转义HTML
export function htmlUnEscapeAll(text) {
  return (text || "").replace(/&lt;/g, "<").replace(/&gt;/g, ">").replace(/&nbsp;/g, " ").replace(/&amp;/g, "&");
}

// 正则解析将\n换位换行，正则匹配链接,customMsg自定义消息,key关键词,reEditor重新编辑
export function strToHtml(text = "", notLink, customMsg, strKey, reEditor) {
  // 过滤xss
  text = htmlEscapeAll(text, true);
  // 关键词匹配
  if (strKey) {
    text = getHighlight(text, strKey);
  } else {
    // 替换@文档表
    let replaceMap = {};
    // 解析@人员消息
    if (customMsg && customMsg.custom && (customMsg.custom.hait || customMsg.custom.docId)) {
      let haitIndexList = [];
      let haitPosition = customMsg.custom.haitPosition || [];
      let docPos = customMsg.custom.docPos || [];
      let docName = customMsg.custom.docName;
      let docId = customMsg.custom.docId;
      text.replace(/@/g, (item, key) => {
        haitIndexList.push(key);
        return "@";
      });
      for (let i = haitIndexList.length - 1; i >= 0; i--) {
        let haitIndex = haitPosition.findIndex(item => {return item == i});
        let docIndex = docPos.findIndex(item => {return item == i});
        if (haitIndex > -1) {
          // @人员开始位置
          let haitStartIndex = haitIndexList[i];
          if (haitStartIndex != null) {
            // 查找第二个空格的位置
            let haitEndIndex = text.indexOf(" ", text.indexOf(" ", haitStartIndex) + 1);
            let haitStr = text.substring(haitStartIndex, haitEndIndex);
            // 匹配不上两个空格则改为一个空格
            if (!/.+\s\(.*\)/.test(haitStr) && !/@ 全员/.test(haitStr)) {
              haitEndIndex = text.indexOf(" ", haitStartIndex);
              haitStr = text.substring(haitStartIndex, haitEndIndex);
            }
            let isSelf = (getUserInfo().workerNo == customMsg.custom.hait[haitIndex] || customMsg.custom.hait[haitIndex] == "all") && !customMsg.notSelf;
            if (reEditor) {
              text = `${text.substring(0, haitStartIndex)}<img class="im-image-hait ${reEditor ? "reEditor " : ""}" data-hait-text=" ${haitStr} " data-hait-account="${customMsg.custom.hait[haitIndex]}" src="${strToImg(haitStr, isSelf)}">${text.substring(haitEndIndex, text.length)}`;
            } else {
              text = `${text.substring(0, haitStartIndex)}<span class="hait-text ${isSelf ? "hait-self" : ""}">&nbsp;${haitStr}&nbsp;</span>${text.substring(haitEndIndex, text.length)}`;
            }
          }
        } else if (docIndex > -1) {
          // @文档开始位置
          let docStartIndex = haitIndexList[i];
          if (docStartIndex != null) {
            // 查找文档名位置
            let docEndIndex = text.indexOf(docName[docIndex], docStartIndex) + docName[docIndex].length;
            let haitStr = text.substring(docStartIndex, docEndIndex);
            // 正则后替换
            let replaceText = `$replaceText$replaceText-${docStartIndex}`;
            replaceMap[replaceText] = docName[docIndex];
            let dataStr = `data-hait-key="${docId[docIndex]}" data-hait-value="@${replaceText} " data-hait-name="doc"`;
            text = `${text.substring(0, docStartIndex)}<img class="im-image-hait-new im-image-hait-doc ${reEditor ? "reEditor" : ""}" ${dataStr} title="在浏览器中打开 ${haitStr}" src="${strToImg(haitStr, "", {underline: true, type: "doc"})}">${text.substring(docEndIndex, text.length)}`;
          }
        }
      }
    }
    // 解析@文档二期
    if (customMsg && customMsg.type == "document" && customMsg.file) {
      let haitStr = `@${customMsg.file.docName}`;
      let dataStr = `data-hait-key="${customMsg.file.docPadId}" data-hait-value="${haitStr} " data-hait-name="doc" data-hait-ext=".docx" data-hait-empnumber="${customMsg.file.empNumber}" data-hait-docid="${customMsg.file.docId}"`;
      if (customMsg.file.docImg) {
        dataStr += " data-hait-docImg=" + customMsg.file.docImg;
      }
      if (customMsg.file.property) {
        dataStr += "  data-hait-property=" + customMsg.file.property;
      }
      let dataInfo = customMsg.file;
      text = `<img class="im-image-hait-new im-image-hait-doc ${reEditor ? "reEditor" : ""}" data-info=${JSON.stringify(dataInfo)} ${dataStr} src="${strToImg(haitStr, "", {underline: true, type: "doc"})}">`;
    }

    // 不转义连接
    if (!notLink) {
      // 判断人名+时间文本转为灰色字体提示
      text = text.replace(/([\s]|^)(.{2,5})(\(.{1,}\))*\s\(.{1,}\)\s\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}[\s]/g, item => {
        return `<span class="show-name-gray">${item}</span>`
      });
      // 链接文本转a标签
      text = getLink(text);
    }

    // 替换@文档名
    for (let key in replaceMap) {
      text = text.replace(key, replaceMap[key]);
    }
  }
  return text.replace(/\n/g, "<br>");
}

// 文字高亮html
export function getHighlight(text, strKey) {
  if (!strKey) {
    return text;
  }
  return text.replace(new RegExp(regReplace(strKey), "g"), item => {return `<span class="highlight">${strKey}</span>`})
}

// 链接格式化html,text-文本，flag-返回正则
export function getLink(text, flag) {
  let linkReg = /(((ht|f)tps?):\/\/[\w\-]+(\.[\w\-]+)+|(([0-9]{1,3}\.){3}(:[0-9]+)?)|(([a-z0-9\-]+\.)+(cn|com|edu|gov|net|nom|org|xyz|top|info|hk){1})){1}([\w\-\.,?;^=%&:\/~\+#]*[\w\-\?^=%&\/~\+#])?/gi;
  if (flag) {
    return linkReg;
  } else {
    // 简易匹配减少cpu消耗
    if (/[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?/.test(text)) {
      return text.replace(linkReg, item => {
        return `<a class="im-link" href="${linkFormat(item)}" title="在浏览器打开 ${item}" target="_blank">${item}</a>`
      });
    } else {
      return text;
    }
  }
}

// 链接格式化-带上http/https
export function linkFormat(url) {
  if (!url) {
    return "";
  }
  return /((ht|f)tps?):\/\//i.test(url) ? url : `http://${url}`;
}

// 链接跳转浏览器
export function setOpenExternal(url) {
  remote.store.dispatch("setOpenWindow", [linkFormat(url), "login"]);
}

// 文字转图片-str文字-flag改变底色，param-underline文字加下划线
export function strToImg(str, flag, param) {
  str = " " + str + " ";
  let canvas = document.getElementById("strToImg");
  if (!canvas) {
    canvas = document.createElement("canvas");
    canvas.id = "strToImg";
    canvas.style.display = "none";
    document.body.appendChild(canvas);
  }
  let ctx = canvas.getContext("2d");
  // 适配屏幕缩放 @图片模糊问题
  let ratio = window.devicePixelRatio || 1;
  // 计算宽高
  let height = 20 * ratio;
  let font = (height - 7) + "px 微软雅黑,Helvetica,Arial,sans-serif";
  // let textBaseline = "hanging";
  let textBaseline = "bottom";
  ctx.font = font;
  ctx.textBaseline = textBaseline;
  let width = ctx.measureText(str).width;
  let textX = 0;
  canvas.height = height;
  canvas.width = width;
  // 清空画布
  ctx.clearRect(0, 0, width, height);
  // 绘制背景
  if (flag) {
    ctx.fillStyle = "#FEB673";
    ctx.fillRect(0, 0, width, height);
  }
  // 绘制文字
  if (flag) {
    ctx.fillStyle = "#000000";
  } else {
    ctx.fillStyle = "#00A2FF";
  }
  ctx.font = font;
  ctx.textBaseline = textBaseline;
  ctx.fillText(str, textX, height - 2);
  // 新增文字下划线
  if (param && param.underline) {
    ctx.strokeStyle = "#1AAAF0";
    ctx.lineWidth = 2 * ratio;
    ctx.moveTo(2, height);
    ctx.lineTo(ctx.measureText(str).width + textX - 2, height);
    ctx.stroke();
  }
  return canvas.toDataURL();
}

// 获取头像前缀
export function getAvatar(headpic) {
  if (headpic) {
    headpic = /https?:\/\//.test(headpic) ? headpic : config[config.env].jjsResUrl + (headpic.slice(0, 1) != "/" ? "/" : "") + headpic;
  } else {
    headpic = "/img/default/p2p.png";
  }
  return headpic;
}

// 加载头像错误
export function avatarError(scene, to, type, e) {
  let session = {scene: scene, to: to, detailInfo: {detailType: type}};
  let src = getSessionType(session).avatar || "/img/default/p2p.png";
  e.target.src = src;
  e.target.onerror = ""
}

// 房源图片加载错误
export function fangError(e) {
  e.target.src = "/img/fang_default.png";
  e.target.onerror = ""
}

// 设置人员基本信息
export function setUserBaseInfo(item = {}) {
  // 从服务器获取
  let workerNo = item.workerNo || item.nimAccid;
  if (new RegExp(config.ai).test(workerNo)) {
    // 兼容数字人账号
    let updateTime = Date.now() + remote.store.getters.getDiffTime;
    item.workerNo = workerNo;
    item.name = (item.workerName || item.name || item.workerNo) + " ";
    item.userName = item.name;
    item.workerName = item.name
    item.avatar = getAvatar(item.headImg || item.headPic) + "?imageView2/2/w/200/h/200";
    item.avatarOther = getAvatar(item.halfBodyPic || item.headImg || item.headPic);
    item.sex = item.gender ? (item.gender == "1" ? "男" : "女") : "";
    item.selfIntro = item.briefIntr;
    item.appTypeId = item.id;
    item.leaderName = item.superiorName;
    item.region = item.managerName;
    item.localAddTime = updateTime;
    item.updateTime = updateTime;
  } else {
    // 直营看直营名
    item.name = item.workerName ? (item.workerName + (item.deptName ? " (" + item.deptName + ")" : "")) : item.name || "";
    item.avatar = item.headPic ? getAvatar(item.headPic) + "?imageView2/2/w/200/h/200" : item.avatar || "-";
    item.avatarOther = getAvatar(item.halfBodyPic || item.headPic);
    item.gender = item.sex ? (item.sex == "1" ? "男" : "女") : "";
    item.userName = item.workerName || item.workerNo;
    // 直营1加盟2
    if (!item.empType) {
      item.empType = new RegExp(config.jm).test(item.workerNo) ? 2 : 1;
    }
    // 产品暂定直营全部显示 乐有家
    if (item.empType == 1) {
      item.compName = "乐有家";
    }
    // 直营看加盟和加盟看直营/加盟名都显示 名字(部门-公司)，部门和公司一致显示 名字(部门)
    item.otherShowName = item.workerName || "";
    if (item.deptName || item.compName) {
      item.otherShowName += "（";
      if (item.deptName == item.compName || !item.compName) {
        item.otherShowName += item.deptName;
      } else if (!item.deptName) {
        item.otherShowName += item.compName;
      } else {
        item.otherShowName += item.deptName + "-" + item.compName;
      }
      item.otherShowName += "）";
    }
  }
  // 设置显示视角字段
  item.userShowName = getUserInfo().empType == 2 || item.empType == 2 ? (item.otherShowName || item.name) : item.name;
  return item;
}

// 获取会话类型，返回类型和默认头像
// 1-讨论组 2-高级群 3超大群 4-p2p 5-房产网 6-群通知 7-群助手 8-订阅号 9-服务号 10-全部群 11-全部订阅号和服务号
// 12-进线客户讨论组 13-客户咨询 14-售后讨论组(不可查看/退出/解散) 15-限制讨论组(可以查看/聊天不可退出/解散/拉人/操作)
// 16-群ai头像
export function getSessionType(session) {
  if (!session) {
    return {};
  }
  let type = "";
  let name = session.to;
  let avatar = "";
  let prologue = "";
  if (session.scene == "team") {
    if (session.detailInfo?.detailType == "group") {
      avatar = "/img/default/group.png";
      type = getTeamType(session.detailInfo);
    } else {
      avatar = "/img/default/team.png";
      type = 2;
    }
  } else if (session.scene == "superTeam") {
    avatar = "/img/default/team.png";
    type = 3;
  } else if (session.scene == "p2p") {
    if (new RegExp(config.fcw).test(session.to)) {
      avatar = "/img/default/fcw.png";
      name = setFcwUser(session).name;
      type = 5;
    } else if (new RegExp(config.systemMessage).test(session.to)) {
      name = "群通知"
      avatar = "/img/default/qtz.png";
      type = 6;
    } else if (new RegExp(config.helperAccount).test(session.to)) {
      name = "群助手"
      avatar = "/img/default/qzs.png";
      type = 7;
    } else if (new RegExp(config.customerAccount).test(session.to)) {
      name = "客户咨询"
      avatar = "/img/default/khzx.png";
      type = 13;
    } else if (session.detailInfo?.detailType == "teamAi") {
      name = "群问答助手"
      avatar = "/img/default/ai.png";
      type = 16;
      prologue = "我是本群的AI群助手，我能帮大家根据知识库中的内容，解答相关问题";
    } else {
      avatar = "/img/default/p2p.png";
      // ai默认头像
      if (new RegExp(config.ai).test(session.to)) {
        avatar = "/img/default/ai.png";
      }
      if (new RegExp(config.subscribe).test(session.to)) {
        if (config.subscribe == session.to) {
          name = "乐聊订阅号";
          avatar = "/img/default/dyh.png";
        }
        type = 8;
      } else if (new RegExp(config.serveNumber).test(session.to)) {
        type = 9;
      } else {
        type = 4;
      }
    }
  }
  return {type: type, name: name, avatar: avatar, to: session.to, prologue: prologue};
}

// 获取讨论组类型
// 12-进线客户讨论组(只有群主显示) 14-售后讨论组(不可查看/退出/解散)(都不显示) 15-限制讨论组(可以查看/聊天不可退出/解散/拉人/操作)
export function getTeamType(teamInfo) {
  let type = 1;
  if (teamInfo.groupType == 2) {
    type = 12;
  } else if (teamInfo.groupType == 3 || teamInfo.groupDisabled == 1) {
    type = 14;
  } else if (teamInfo.groupType == 4 || teamInfo.groupDisabled == 2) {
    type = 15;
  }
  return type;
}

// 是否是不可操作的群组
export function isDisabledGroup(sessionInfo) {
  let detailInfo = sessionInfo.detailInfo;
  return detailInfo && (detailInfo.groupType == 2 || detailInfo.groupType == 3 || detailInfo.groupType == 4 || detailInfo.groupDisabled);
}

// 判断是否订阅号和服务号
export function isSubOrSer(id) {
  return new RegExp(config.subscribe).test(id) || new RegExp(config.serveNumber).test(id)
}

// 选中元素（比如图片）
export function selElm(elm) {
  let sel = window.getSelection();
  sel.removeAllRanges();
  let rang = document.createRange();
  rang.selectNode(elm);
  sel.addRange(rang);
}

// 设置公众号服务号html
export function setSubSerHtml(html) {
  return (html || "").replace(/\n/g, "<br>");
}

// 隐藏元素
export function hideElm(e) {
  e.target.style.display = "none";
}

// 毫秒数转时分秒
export function secToTime(milliseconds) {
  let seconds = Math.floor(milliseconds / 1000);
  let hours = Math.floor(seconds / 3600);
  let minutes = Math.floor((seconds % 3600) / 60);
  seconds = seconds % 60;

  if (hours == 0) {
    return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  } else {
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  }
}

// 获取输入框数据
export function getInputMessage(ops) {
  let messages = [];
  let imgCount = 0;
  // 将输入框内容转为消息内容
  for (let i = 0; i < ops.length; i++) {
    let item = ops[i];
    if (!item) {
      continue;
    }
    if (typeof item.insert == "string") {
      if (i == ops.length - 1) {
        item.insert = item.insert.replace(/\n$/ig, "");
      }
      if (item.insert == "") {
        continue;
      }
      // 处理文本消息
      let inputParam = {
        messages: messages,
        type: "text",
        text: item.insert
      }
      messages = dealInputMessage(inputParam);
    } else if (typeof item.insert === "object") {
      // imImage imEmoji imHaitImage imHaitImageNew只存在一种
      let imObj = item.insert["imEmoji"] || item.insert["imHaitImage"] || item.insert["imImage"] || item.insert["imHaitImageNew"] || item.insert["imDefaultImage"];
      // 处理特定消息
      let inputParam = {
        messages: messages,
        type: "text",
        text: imObj.text,
        account: imObj.account || imObj.key
      }
      if (imObj.img && imObj.node) {
        // 图片类型
        inputParam.img = imObj.img;
        inputParam.width = imObj.node.naturalWidth;
        inputParam.height = imObj.node.naturalHeight;
        inputParam.type = "image";
        if (imObj.base64 && "[图片]" != imObj.base64) {
          inputParam.img = imObj.base64;
        }
        imgCount++;
      } else if (item.insert["imHaitImageNew"]) {
        // @文档类型
        inputParam.type = imObj.name;
        inputParam.text = imObj.value;
        inputParam.account = imObj.key;
        inputParam.other = {
          empNumber: imObj.empNumber,
          docId: imObj.docId,
          docImg: imObj.docImg,
          property: imObj.property,
        }
      } else if (item.insert["imDefaultImage"]) {
        inputParam = null;
        messages.push({sendFileFlag: true, type: "break", ...JSON.parse(decodeURIComponent(imObj.value))});
      }
      if (inputParam) {
        messages = dealInputMessage(inputParam);
      }
    }
  }
  return {messages: messages, imgCount: imgCount};
}

// 整理输入框数据
export function dealInputMessage(param) {
  let {messages, type, text, img, account, height, width, file, other} = param;
  if (type == "image") {
    // 图片
    messages.push({
      type: "image",
      url: img,
      height: height,
      width: width,
      file: {
        url: img
      }
    });
  } else if (type == "text" || type == "doc") {
    // 文本
    let content = messages[messages.length - 1];
    if (!content || content.type == "image" || content.type == "document" || content.type == "break") {
      if (account) {
        if (type == "doc") {
          // @文档
          messages.push({
            type: "document",
            file: {
              docPadId: account,
              docName: text.slice(1, text.length - 1),
              docId: other.docId,
              empNumber: other.empNumber,
              docImg: other.docImg,
              property: other.property,
            }
          });
        } else {
          // @文本
          messages.push({
            type: "text",
            text: text,
            hait: [account],
            haitPosition: [text.match(/@/g).length - 1],
            atName: [text],
          });
        }
      } else {
        // 普通文本
        messages.push({
          type: "text",
          text: text.replace(/\ufeff/g, "")
        });
      }
    } else {
      if (account) {
        if (type == "doc") {
          // @文档
          messages.push({
            type: "document",
            file: {
              docPadId: account,
              docName: text.slice(1, text.length - 1),
              docId: other.docId,
              empNumber: other.empNumber,
              docImg: other.docImg,
              property: other.property,
            }
          });
        } else {
          // @文本
          content.text += text;
          content.hait = content.hait || [];
          content.hait.push(account);
          content.haitPosition = content.haitPosition || [];
          content.haitPosition.push(content.text.match(/@/g).length - 1);
          content.atName = content.atName || [];
          content.atName.push(text);
        }
      } else {
        // 普通文本
        content.text += text.replace(/\ufeff/g, "");
      }
    }
  } else if (type == "file") {
    // 文件
    file.url = file.path;
    messages.push({
      type: "file",
      file: file
    });
  }
  return messages;
}

// dataUrl转blob、file
export function dataUrlToBlob(dataUrl) {
  let arr = dataUrl.split(",");
  let mime = arr[0].match(/:(.*?);/)[1];
  let bstr = atob(arr[1]), n = bstr.length, u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], {type: mime});
}

// 获取消息推送文案
export function getPushContent(param) {
  let {userName, teamName, text} = param;
  let res = (teamName ? `${userName}(${teamName})` : userName) + ":" + text;
  return res.slice(0, 100);
}

// 获取消息自定义内容
export function getMsgCustom(param) {
  let custom = {};
  let {quoteMsg, messages} = param;
  // 引用
  if (quoteMsg) {
    custom.quoteTxt = quoteMsg.quoteSendTxt;
    custom.quoteType = quoteMsg.quoteType;
    custom.quoteImageList = quoteMsg.quoteImageList;
    custom.quoteImageLength = quoteMsg.quoteImageLength;
    custom.serverID = quoteMsg.msg.idServer;
    custom.clientID = quoteMsg.msg.idClient;
    custom.scene = quoteMsg.msg.scene;
    custom.from = quoteMsg.msg.from;
    custom.time = quoteMsg.msg.time;
  }
  if (messages) {
    for (let i = 0; i < messages.length; i++) {
      // @人员消息结构
      if (messages[i].hait && messages[i].hait.length > 0) {
        if (!custom.hait) {
          custom.hait = [];
        }
        if (!custom.haitPosition) {
          custom.haitPosition = [];
        }
        if (!custom.atName) {
          custom.atName = [];
        }
        custom.hait = custom.hait.concat(messages[i].hait);
        custom.haitPosition = custom.haitPosition.concat(messages[i].haitPosition);
        custom.atName = custom.atName.concat(messages[i].atName);
      }
      // @文档消息结构
      if (messages[i].docName && messages[i].docName.length > 0) {
        if (!custom.docId) {
          custom.docId = [];
        }
        if (!custom.docName) {
          custom.docName = [];
        }
        if (!custom.docPos) {
          custom.docPos = [];
        }
        custom.docId = custom.docId.concat(messages[i].docId);
        custom.docName = custom.docName.concat(messages[i].docName);
        custom.docPos = custom.docPos.concat(messages[i].docPos);
      }
    }
  }
  return custom;
}

// 去除消息前后空格
export function getMsgTrim(msg) {
  let lastTrimIndex = -1;
  for (let i = 0; i < msg.length; i++) {
    if (!msg[i] || (msg[i].type == "text" && !msg[i].text.trim() && (msg[i - 1]?.type == "document" || msg[i + 1]?.type == "document"))) {
      msg.splice(i, 1);
      i--;
    } else if (msg[i].type == "text") {
      // 去除乐文档前后换行
      if (msg[i - 1]?.type == "document" && /^\n*/.test(msg[i].text)) {
        msg[i].text = msg[i].text.replace(/^\n*/, "");
      } else if (msg[i + 1]?.type == "document" && /\n*$/.test(msg[i].text)) {
        msg[i].text = msg[i].text.replace(/\n*$/, "");
      }
    }
    // 去除最后都是空格数据
    if (lastTrimIndex == -1 && msg[i]?.type == "text" && !msg[i].text.trim()) {
      lastTrimIndex = i;
    } else if (lastTrimIndex > -1) {
      lastTrimIndex = -1;
    }
  }
  // 去除最后几个空白换行
  if (lastTrimIndex > -1) {
    msg = msg.slice(0, lastTrimIndex);
  }
  // 去除最后一条消息的后换行
  let lastIndex = msg.length - 1;
  if (msg[lastIndex]?.type == "text") {
    msg[lastIndex].text = msg[lastIndex].text.replace(/\n*$/, "");
  }
  return msg;
}

// 获取用户信息
export function getUserInfo() {
  let userInfo = remote.global.userInfo;
  return JSON.parse(userInfo ? decrypt(userInfo) : "{}")
}

// 定义一个深拷贝函数  接收目标target参数
export function deepClone(any) {
  return cloneDeep(any)
}

// 子窗口深拷贝
export function deepCloneWin(target) {
  // 定义一个变量
  let result;
  // 如果当前需要深拷贝的是一个对象的话
  if (typeof target === "object") {
    // 如果是一个数组的话
    if (Array.isArray(target)) {
      result = []; // 将result赋值为一个数组，并且执行遍历
      for (let i in target) {
        // 递归克隆数组中的每一项
        result.push(deepCloneWin(target[i]))
      }
      // 判断如果当前的值是null的话；直接赋值为null
    } else if (target === null) {
      result = null;
      // 判断如果当前的值是一个RegExp对象的话，直接赋值
    } else if (target.constructor === RegExp) {
      result = target;
    } else {
      // 否则是普通对象，直接for in循环，递归赋值对象的所有值
      result = {};
      for (let i in target) {
        result[i] = deepCloneWin(target[i]);
      }
    }
    // 如果不是对象的话，就是基本数据类型，那么直接赋值
  } else {
    result = target;
  }
  // 返回最终结果
  return result;
}

// 是否显示二级列表状态-订阅号、系统通知、群助手
export function isSessionList(sessionInfo) {
  return new RegExp(`^p2p-(${config.subscribe}|${config.systemMessage}|${config.helperAccount}|${config.customerAccount})$`).test(sessionInfo.id)
}

// 另存为
export function selFolderDialog(param) {
  // 文件类型
  param.defaultPath = removeReplace(param.defaultPath);
  return new Promise(resolve => {
    let emitValue = {nwsaveas: param.defaultPath, done: files => {resolve(files[0].path)}};
    if (param.globalEmit) {
      param.globalEmit.fileInput = emitValue;
    } else {
      window.store.commit("setEmit", {type: "fileInput", value: emitValue});
    }
  });
}

// 打开文件
export function openLocalFile(fileLocal) {
  remote.Shell.openItem(fileLocal);
}

// 打开文件夹
export function openLocalFolder(filePath, fileName) {
  fs.stat(filePath + fileName, function (error, info) {
    if (error) {
      remote.Shell.openExternal(filePath);
    } else {
      remote.Shell.showItemInFolder(filePath + fileName);
    }
  });
}

// 判断快捷键是否注册成功
export function isRegistered(key) {
  return window.global.registerMap[key] && !window.global.registerMap[key].isNotRegister;
}

// 获取全局变量
export function getGlobal(key) {
  return remote.global[key];
}

// 右键菜单
export function showMenu(menuList) {
  emitMsg("msg", {type: "global", setGlobal: 1, info: {menuList: menuList}});
  let thisWin = nw.Window.get();
  let menu = {
    list: menuList,
    popup: function (x, y, hover, w, h) {
      return textmenu({x: x + 4, y: y + 4, win: {width: thisWin.width, height: thisWin.height}, hover: hover || false, w: w, h: h});
    },
    hide: function () {
      textmenu({win: {width: thisWin.width, height: thisWin.height}, hide: true});
    }
  }
  return menu;
}

// 右键追加菜单
export function addMenu(menu, pos, item) {
  menu.list.splice(pos, 0, item);
  emitMsg("msg", {type: "global", setGlobal: 1, info: {menuList: menu.list}});
  return menu;
}

// 右键更改菜单
export function editMenu(menu, pos, item) {
  menu.list.splice(pos, 1, item);
  emitMsg("msg", {type: "global", setGlobal: 1, info: {menuList: menu.list}});
  return menu;
}

//返回文件图标的路径
export function getFileIcon(ext) {
  let exrPng = "";
  if (ext == "document") {
    // 乐文档类型
    exrPng = "document.png";
  } else if (ext == "geo") {
    // 地理位置
    exrPng = "geo.png";
  } else if (/^(ppt|pps|pptx)$/i.test(ext)) {
    exrPng = "ppt.png";
  } else if (/^(pdf)$/i.test(ext)) {
    exrPng = "pdf.png";
  } else if (/^(doc|docx|dot|dotx|docm)$/i.test(ext)) {
    exrPng = "word.png";
  } else if (/^(zip|7z|rar|tar|gzip|jar)$/i.test(ext)) {
    exrPng = "zip.png";
  } else if (/^(mp4|rmvb|avi|ts|mov|rm|flv|mp3)$/i.test(ext)) {
    exrPng = "mp4.png";
  } else if (/^(xls|xlsx|csv)$/i.test(ext)) {
    exrPng = "xlsx.png";
  } else if (/^(png|gif|jpg|jpeg|webp)$/i.test(ext)) {
    exrPng = "img.png";
  } else {
    exrPng = "other.png";
  }
  return "/img/index/msg/folder/" + exrPng;
}

// 获取元素距离top和left（废弃）
export function getOffset(obj) {
  if (!obj) {
    return {};
  }
  let top = obj.offsetTop;
  let left = obj.offsetLeft;
  let parent = obj.offsetParent;
  while (parent != null) {
    top += parent.offsetTop;
    left += parent.offsetLeft;
    parent = parent.offsetParent;
  }
  return {top: top, left: left};
}

// 获取元素信息
export function getBounding(obj) {
  return obj.getBoundingClientRect();
}

// 获取房产网用户名和头像
export function setFcwUser(item) {
  item.workerNo = item.account || item.to;
  item.name = item.alias || "潜客" + item.workerNo;
  item.avatar = "/img/default/fcw.png";
  return item;
}

// 返回房产网用户信息
export function getFcwInfo(info) {
  if (info && info.account) {
    let isArray = true;
    let account = info.account;
    if (!Array.isArray(info.account)) {
      isArray = false;
      account = [account];
    }
    let personInfo = account.map(item => {return info.nimFriend[item] || setFcwUser({account: item})});
    // 单个返回
    return isArray ? personInfo : personInfo[0];
  }
}

// 获取用户手机号排序（去重）
export function getUserTel(user) {
  //phoneTel 私人手机号码,tel 其他电话,phone 福利手机
  let phoneList = [];
  if (user.phoneTel) {
    phoneList.push(user.phoneTel.trim());
  }
  if (user.tel && user.phoneTel != user.tel) {
    phoneList.push(user.tel.trim());
  }
  if (user.phone && user.phoneTel != user.phone && user.tel != user.phone) {
    phoneList.push(user.phone.trim());
  }
  return phoneList;
}

// 正则获取乐乐表情
export function getXLMatchedStr(str) {
  let reg = /\[(乐乐.+?)\]/;
  let reg_g = /\[(乐乐.+?)\]/g;
  let result = (str || "").match(reg_g);
  let list = [];
  if (!result) {
    return;
  }
  for (let i = 0; i < result.length; i++) {
    let item = result[i]
    list.push(item.match(reg)[1])
  }
  return list;
}

// 群成员排序
export function sortTeamMembers(teamMembers) {
  // 入群时间排序后，按群主、管理员、普通成员排序
  let sortBy = {"owner": 1, "manager": 2, "normal": 3};
  return teamMembers.sort((a, b) => a.joinTime - b.joinTime).sort((a, b) => sortBy[a.type] - sortBy[b.type]);
}

// 会话排序（去重）
export function sortSessions(sessions) {
  // 返回会话时间排序
  sessions.sort((a, b) => {return (b.updateTime || 0) - (a.updateTime || 0)});
  // 返回置顶排序
  sessions.sort((a, b) => {return ((b.alwaysTop || 0) - (a.alwaysTop || 0)) || ((b.isTop || false) - (a.isTop || false))});
  let obj = {};
  return sessions.reduce(function (item, next) {
    obj[next.id] ? '' : obj[next.id] = true && item.push(next);
    return item;
  }, []);
}

// 数组去重
export function filterArr(arr, key) {
  let obj = {};
  return arr.reduce(function (item, next) {
    obj[next[key]] ? '' : obj[next[key]] = true && item.push(next);
    return item;
  }, []);
}

// 默认会话列表未读数
export function getDefaultSessionUnread(item) {
  let unread = 0
  // 群助手、接收不通知的群、订阅号不参与未读数统计
  if (item.unread && !item.isNoTip && !new RegExp(config.subscribe).test(item.id)) {
    unread += item.unread || 0;
  }
  return unread;
}

// 搜索关键词正则特殊字符过滤
export function regReplace(text = "", flag) {
  let reg = new RegExp("[~*.?+$^\\[\\](){}|\\/\\\\%]", "g");
  if (flag) {
    return reg.test(text);
  } else {
    return text.replace(reg, item => {return "\\" + item});
  }
}

// 去除特殊字符-用于下载
export function removeReplace(text, flag) {
  let reg = new RegExp("[*?:|\\/\\\\<>\"]", "g");
  if (flag) {
    return reg.test(text);
  } else {
    return text.replace(reg, item => {return ""});
  }
}

// 获取智能日程本地状态
export function getScheduleLocalStatus(item) {
  let userInfo = getUserInfo();
  try {
    if (userInfo.workerNo == item.toNo) {
      item.status = item.toStatus;
    } else if (userInfo.workerNo == item.fromNo) {
      item.status = item.fromStatus;
    }
    let participateList = (item.participateIds || "").split(",");
    let participateStatusList = (item.participateStatuss || "").split(",");
    item.keyIndex = participateList.findIndex(function (thisItem) {
      return userInfo.workerId == thisItem
    });
    if (item.keyIndex != -1 && participateStatusList[item.keyIndex]) {
      item.status = participateStatusList[item.keyIndex];
    }
    let localSchedule = JSON.parse(localStorage.getItem("smartSchedule") || "{}");
    let localScheduleJson = "";
    if (localSchedule[userInfo.workerNo] && localSchedule[userInfo.workerNo][item.scheduleId]) {
      localScheduleJson = localSchedule[userInfo.workerNo][item.scheduleId];
    }
    if (localScheduleJson.status && localScheduleJson.time > item.msgTime) {
      item.status = localScheduleJson.status;
    }
    // 是参与者则显示状态
    item.showStatus = item.keyIndex != -1 || item.status;
    if (
      ((userInfo.workerNo == item.toNo && item.toDel == 1) || (userInfo.workerNo == item.fromNo && item.fromDel == 1) || item.keyIndex == -1) &&
      (!localScheduleJson || (localScheduleJson && localScheduleJson.time < item.msgTime.time))
    ) {
      item.showStatus = false;
      item.status = "";
    }
  } catch (e) {
    console.log("getScheduleStatusErr", e)
  }
  return item;
}

// 获取日程显示时间
export function getScheduleTime(item, type) {
  let dataStr = "MM/dd";
  if (type == 1) {
    dataStr = "MM月dd日";
  }
  let showTime = "";
  // 0-当天 1-全天日程 2-跨天日程
  if (item.dayType == 2) {
    showTime = dateFormat(item.startTime, dataStr + " HH:mm") + "-" + dateFormat(item.endTime, dataStr + " HH:mm");
  } else if (item.dayType == 1) {
    showTime = dateFormat(item.startTime, dataStr);
    let endTime = dateFormat(item.endTime, dataStr);
    if (endTime != showTime) {
      showTime += "-" + endTime;
    }
  } else {
    showTime = dateFormat(item.startTime, dataStr + " HH:mm") + " - " + dateFormat(item.endTime, "HH:mm");
  }
  return showTime;
}

// 处理消息平台接口
export function dealMsgCenterContent(item) {
  let thisContent = [];
  let thisContentLink = [];
  item.defaultContent = deepClone(item.content);
  item.content.data.content.map(item1 => {
    if (item1.type == "link_button" || item1.type == "custom_button") {
      // 最多显示2个按钮
      if (thisContentLink.length < 2) {
        if (item1.type == "link_button") {
          thisContentLink.push(item1);
        } else if (item1.type == "custom_button" && item1.pc && (item1.pc.url || item1.pc.pcClickTips)) {
          // 自定义按钮url放到外层
          item1.url = item1.pc.url;
          item1.urlType = item1.pc.type;
          item1.pcClickTips = item1.pc.pcClickTips;
          thisContentLink.push(item1);
        }
      }
    } else {
      // 插入按钮数组
      if (thisContentLink.length > 0) {
        thisContent.push({type: "button", value: thisContentLink});
        thisContentLink = [];
      }
      thisContent.push(item1);
    }
  });
  // 插入按钮数组
  if (thisContentLink.length > 0) {
    thisContent.push({type: "button", value: thisContentLink});
    thisContentLink = [];
  }
  return thisContent;
}

// 和主窗口通讯-background.js中的ipcMain
export function emitMsg(channel, param) {
  switch (param.type) {
    case "shortcut":
      // 注销所有快捷键
      if (param.unregisterAll) {
        for (let key in window.global.registerMap) {
          if (!window.global.registerMap[key].isNotRegister) {
            mainNW.App.unregisterGlobalHotKey(window.global.registerMap[key]);
          }
          delete window.global.registerMap[key];
        }
        return;
      }
      // 注销快捷键
      if (param.unregister && window.global.registerMap[param.unregister]) {
        try {
          mainNW.App.unregisterGlobalHotKey(window.global.registerMap[param.unregister]);
        } catch (e) {}
        delete window.global.registerMap[param.unregister];
      }
      // 注册快捷键
      if (param.register) {
        let registerParam = new mainNW.Shortcut({
          key: param.register,
          active: function () {
            console.log("globalShortcut success", param.register);
            remote.Window.get().window.store.commit("setEmit", {type: "imActivity", value: Date.now()});
            let currentWindow = remote.Window.get();
            if (param.register == config.shortcut.lockIm) {
              // 锁定乐聊
              currentWindow.window.store.commit("setEmit", {type: "lockIm", value: new Date().getTime()});
            } else if (param.register == config.shortcut.toggleIm) {
              // 切换显示乐聊
              if (currentWindow.isFocused) {
                currentWindow.window.store.commit("setWindowMin", currentWindow.cWindow.id);
              } else {
                currentWindow.window.store.commit("setWindowCancelMin", currentWindow.cWindow.id);
              }
            } else if (param.register == config.shortcut.jt1 || param.register == config.shortcut.jt2) {
              // 截图-获取聚焦的窗口，没有默认为主窗口
              let isFocused = currentWindow.isFocused;
              if (!isFocused) {
                for (let key in window.global.childWin) {
                  let thisWin = window.global.childWin[key];
                  // 聊天子窗口聚焦
                  if (key.indexOf("chat-") > -1 && thisWin.isFocused && thisWin.window.store) {
                    currentWindow = thisWin;
                    isFocused = true;
                  }
                }
              }
              currentWindow.window.store.dispatch("setOpenJt", {isFocus: isFocused, key: 2});
            }
          },
          failed: function (msg) {
            window.global.registerMap[param.register].isNotRegister = true;
            console.log("globalShortcut fail-" + msg, param.register);
          }
        })
        window.global.registerMap[param.register] = registerParam;
        mainNW.App.registerGlobalHotKey(registerParam);
      }
      break;
    case "global":
      if (param.setGlobal = 1) {
        // 设置全局变量
        for (let key in param.info) {
          remote.global[key] = param.info[key];
        }
      }
      break;
    case "window":
      console.time("子窗口");
      // 窗口地址
      let url = param.changePath ? param.path : (process.env.NODE_ENV != "development" ? `/index.html#/` : `http://localhost:8888#/`) + param.path;
      if (param.name != "webview") {
        url += `${(url.indexOf("?") > -1 ? "&" : "?")}name=${param.name}`;
      }
      console.log("newWin", encrypt(url));
      // 加载状态的同个窗口不打开多次
      if (window.global.childWinLoadingMap[param.name]) {
        // 5s后还没打开重置状态
        if (Date.now() - window.global.childWinLoadingMap[param.name] > 5 * 1000) {
          delete window.global.childWinLoadingMap[param.name];
        }
        return;
      }
      // 不存在窗口则创建
      try {
        if (window.global.childWin[param.name]) {
          if (param.name == "viewer") {
            window.global.childWin[param.name].window.app.__vue_app__._context.provides.globalEmit.value.reloadImg = Date.now();
          } else if (param.name == "webview" || param.key == "imWebview") {
            window.global.childWin[param.name].window.location.href = url;
            if (window.global.childWin[param.name].width != param.width) {
              window.global.childWin[param.name].width = param.width;
            }
            if (window.global.childWin[param.name].height != param.height) {
              window.global.childWin[param.name].height = param.height;
            }
          } else if (param.name == "codeRecord") {
            window.global.childWin[param.name].focus();
            window.global.childWin[param.name].setBounds({center: true});
            return;
          } else {
            if (window.global.childWin[param.name].window.location.href != url) {
              window.global.childWin[param.name].window.location.href = url;
            }
            window.global.childWin[param.name].reload();
          }
          window.global.childWin[param.name].restore();
          console.timeEnd("子窗口");
          return;
        }
      } catch (e) {
        delete window.global.childWin[param.name];
        remote.store.dispatch("setChildWin", {type: "del", name: param.name});
      }
      let winParam = {
        width: parseInt(param.width),
        height: parseInt(param.height),
        frame: param.frame || false,
        resizable: param.resizable != null ? param.resizable : true,
        transparent: param.transparent || false,
        focus: !param.blur,
        show: !param.hide,
      };
      if (param.taskbar != null) {
        winParam.show_in_taskbar = true;
      }
      if (param.minWidth != null) {
        winParam.min_width = param.minWidth;
        winParam.min_height = param.minHeight;
      }
      if (param.x != null) {
        winParam.x = param.x;
        winParam.y = param.y;
      } else {
        winParam.position = "center";
      }
      if (param.always_on_top) {
        winParam.always_on_top = true;
      }
      window.global.childWinLoadingMap[param.name] = Date.now();
      window.mainNW.Window.open(url, winParam, function (win) {
        console.timeEnd("子窗口");
        // 记录窗口对象
        window.global.childWin[param.name] = win;

        // 设置当前子窗口
        remote.store.dispatch("setChildWin", {name: param.name, id: param.id, win: win.cWindow.id});

        // 聚焦窗口
        win.on("focus", function () {
          win.isFocused = true;
        });
        // 失焦窗口
        win.on("blur", function () {
          win.isFocused = false;
        });
        win.on("loaded", function () {
          delete window.global.childWinLoadingMap[param.name];
          if (param.name == "webview" || param.key == "imWebview") {
            let wind = this;
            wind.window.im = {
              imSavePic: src => {
                return new Promise(async resolve => {
                  await saveSrcToLocal(src);
                  resolve();
                });
              },
              openExternal: url => {
                // 子窗口调用会让主线程奔溃，暂定此处调用不走nim的获取服务器时间方法
                remote.openExternalChild = true;
                setOpenExternal(url);
              },
              // 打开视频
              openVideo: url => {
                openViewer([{src: url, dataSrc: url, w: screen.width, h: screen.height}], 0, screen.width, screen.height, "video");
              },
              // 打开图片
              openViewer: (param) => {
                openViewer(param.imgList, param.index || 0, param.w || screen.width, param.h || screen.height);
              },
              // 获取文件信息
              getFileInfo: item => {
                item.file = item.attach;
                // 乐聊下载不提示
                item.showTips = true;
                remote.store.dispatch("setMsgsLocal", {item: item});
              },
              // 打开文件
              openFile: item => {
                let downloadMD5 = item.file.fileMD5;
                let fileUpload = remote.store.getters.getNimFileUpload(downloadMD5);
                if (fileUpload) {
                  item.file.downloadMD5 = downloadMD5;
                  item.file = fileUpload.file;
                  // 更新下载进度
                  fileUpload.event.on("progress", (param) => {
                    if (param?.key == "del") {
                      delete item.file.progress;
                      delete item.file.downloadMD5;
                      win.window.setProgress(item.file);
                    } else {
                      win.window.setProgress(fileUpload.res);
                      if (fileUpload.res.state == "success") {
                        openLocalFile(fileUpload.res.path + fileUpload.res.name);
                      }
                    }
                  });
                } else {
                  remote.store.dispatch("openFile", {
                    item: item, openType: 3, done: res => {
                      win.window.setProgress(res);
                      if (res.state == "success") {
                        openLocalFile(res.path + res.name);
                      }
                    }
                  });
                }
              },
              // 文件另存为
              selFolderDialog: item => {
                let focusFlag = true;
                remote.store.dispatch("openFolder", {
                  item: item, openType: 1, done: res => {
                    if (focusFlag) {
                      focusFlag = false;
                      wind.focus();
                    }
                    win.window.setProgress(res);
                    if (res.state == "success") {
                      openLocalFile(res.path + res.name);
                    }
                  }
                });
              },
              // 取消下载
              cancelDownload: item => {
                let downloadMD5 = item.file.fileMD5;
                try {
                  remote.store.getters.getNimFileUpload(downloadMD5).abort();
                } catch (e) {}
                remote.store.commit("setNimFileUpload", {key: downloadMD5, value: "del"});
                delete item.file.progress;
                delete item.file.downloadMD5;
                win.window.setProgress(item.file);
              },
              // 语音转文字
              setSpeechToText: (item, callback) => {
                remote.store.dispatch("setSpeechToText", {
                  url: item.attach.url.split("?")[0] + "?audioTrans&type=mp3",
                  idServer: item.msgidServer,
                  done: res => {
                    callback(res);
                  }
                });
              }
            }
          }
          // 全局右键监听
          this.window.document.addEventListener("contextmenu", function (e) {
            e.preventDefault();
            e.stopPropagation();
            return false;
          });
          // 屏蔽F12
          this.window.document.addEventListener("keydown", function (e) {
            if (e.key == "F12") {
              // 屏蔽调试工具
              e.stopPropagation();
              e.preventDefault();
              return false;
            } else if (e.altKey && e.shiftKey && e.ctrlKey && e.key == "P") {
              // 显示调试工具
              win.showDevTools();
              remote.store.dispatch("uploadZxp", {type: 4});
            }
          });
          win.show();
        });

        // 关闭窗口
        win.on("close", function () {
          this.hide();
          this.close(true);
        });
        // 关闭窗口
        win.on("closed", function () {
          win = null;
          remote.store.dispatch("setChildWin", {type: "del", name: param.name});
        });

        // 调整窗口大小
        win.on("resize", function () {
          debounce({
            timerName: "emitResize",
            time: 100,
            fnName: function () {
              // 一分钟记录一次
              if (Date.now() - (global.ResizeLogTime || 0) > 60000) {
                console.log("screens", nw.Screen.screens);
                console.log("winBounds", {w: win?.width, h: win?.height, x: win?.x, y: win?.y});
                global.ResizeLogTime = Date.now();
              }
              if (win?.window?.store) {
                win.window.store.commit("setEmit", {type: "resize", value: {win: win.cWindow.id, time: Date.now()}});
              }
              if (win?.window?.app?.__vue_app__?._context?.provides?.globalEmit?.value) {
                win.window.app.__vue_app__._context.provides.globalEmit.value.resize = {win: win.cWindow.id, time: Date.now()};
              }
              if (win?.width) {
                if (win.maxWidth != win.window.screen.availWidth || win.maxHeight != win.window.screen.availHeight) {
                  win.maxWidth = win.window.screen.availWidth;
                  win.maxHeight = win.window.screen.availHeight;
                  console.log(`setWinMaxSize-${param.name}`, {maxW: win.window.screen.availWidth, maxH: win.window.screen.availHeight});
                  if (param.name != "notify") {
                    win.setMaximumSize(win.window.screen.availWidth, win.window.screen.availHeight);
                  }
                }
              }
            }
          });
        });

        win.on('navigation', function (frame, url, policy) {
          policy.ignore();
          // 在系统默认浏览器打开
          remote.Shell.openExternal(url);
        });

        win.on('new-win-policy', function (frame, url, policy) {
          // 不打开窗口
          policy.ignore();
          if (param.name == "webview" || param.key == "imWebview") {
            try {
              if (/leyoujia.com/.test(new URL(url).host)) {
                win.window.location.href = url;
              } else {
                setOpenExternal(url);
              }
            } catch (e) {
              setOpenExternal(url);
            }
          } else {
            // 在系统默认浏览器打开
            setOpenExternal(url);
          }
        });
      });
      break;
    case "tray":
      let menu = "";
      // 托盘状态 1为登录成功 2为离线 3为退出登录 4为闪烁 5为取消闪烁
      switch (param.tray) {
        case 1:
          if (window.global.trayObj.tray) {
            try {
              window.global.trayObj.tray.remove();
            } catch (e) {
              window.global.trayObj.tray = null;
            }
          }
          // 创建托盘图标
          window.global.trayObj.tray = new mainNW.Tray({title: "乐聊", icon: "/tray_gray.png"});
          // 托盘操作
          window.global.trayObj.tray.tooltip = "乐聊";
          window.global.trayObj.tray.on("click", () => {
            remote.Window.get().window.store.commit("setWindowCancelMin", "main");
            remote.Window.get().window.store.commit("setEmit", {type: "imActivity", value: Date.now()});
          });

          window.global.trayObj.netStatus = 1;
          window.global.trayObj.tray.icon = "/tray.png";
          // 创建托盘菜单
          menu = new mainNW.Menu();
          menu.append(new mainNW.MenuItem({label: "打开新系统", click: function () {remote.Window.get().window.store.commit("setEmit", {type: "toNewSys", value: Date.now()});}}));
          menu.append(new mainNW.MenuItem({label: "设置", click: function () {remote.Window.get().window.store.commit("setEmit", {type: "toSetting", value: Date.now()});}}));
          menu.append(new mainNW.MenuItem({type: "normal", label: "退出", click: () => {quitApp();}}));
          window.global.trayObj.tray.menu = menu;
          break;
        case 2:
          window.global.trayObj.netStatus = 2;
          window.global.trayObj.tray.icon = "/tray_gray.png";
          break;
        case 3:
          if (window.global.trayObj.tray) {
            try {
              window.global.trayObj.tray.remove();
            } catch (e) {
              window.global.trayObj.tray = null;
            }
          }
          // 创建托盘图标
          window.global.trayObj.tray = new mainNW.Tray({title: "乐聊", icon: "/tray_gray.png"});
          // 托盘操作
          window.global.trayObj.tray.tooltip = "乐聊";
          window.global.trayObj.tray.on("click", () => {remote.Window.get().window.store.commit("setWindowCancelMin", "main");});

          window.global.trayObj.netStatus = 2;
          window.global.trayObj.tray.icon = "/tray_gray.png";
          window.global.trayObj.trayFlagInterval = false;
          // 创建托盘菜单
          menu = new mainNW.Menu();
          menu.append(new mainNW.MenuItem({type: "normal", label: "退出", click: () => {quitApp();}}));
          window.global.trayObj.tray.menu = menu;
          break;
        case 4:
          remote.global.trayObj.trayFlagInterval = true;
          break;
        case 5:
          // 清除闪烁
          remote.global.trayObj.trayFlagInterval = false;
          window.global.trayObj.tray.icon = "/tray.png";
          break;
      }
      break;
    case "logout":
      // logout 1返回登录关闭所有页面重新加载 2并清除缓存 其他关闭所有页面重新加载
      if (param.logout == 1) {
        // 重启
        if (process.env.NODE_ENV == "development") {
          // 开发环境重启返回到登录页面
          location.href = `http://localhost:8888#/`;
          // 设置登录窗口大小
          remote.store.commit("setWindowSizeInfo", {type: 1, currentWindow: nw.Window.get().cWindow.id});
          reloadMainWin();
          closeAllWin();
        } else {
          reluanchApp();
        }
      } else {
        reloadMainWin();
        closeAllWin();
      }
      break;
    case "clear":
      // 1-关闭窗口 2-关闭窗口并清除缓存
      if (param.clear == 1) {
        closeAllWin();
      } else if (param.clear == 2) {
        clearAllStorage();
      }
      break;
    default:
      break
  }
}

// 清除缓存
export function clearAllStorage() {
  window.localStorage.clear();
  mainNW.App.clearCache();
  remote.store.commit("setRemoveDB", {workerNo: getUserInfo().workerNo});
  unlinkDir(remote.process.env.LOCALAPPDATA + "\\" + remote.store.state.config.name + "\\User Data\\Default\\IndexedDB");
  remote.Window.get().reload();
  // 清除闪退文件
  remote.store.dispatch("getCrashLogNum", {type: "remove"});
  window.global.clearAllFlag = true;
  reloadMainWin();
  closeAllWin();
}

// 重新加载主窗口
export function reloadMainWin() {
  try {
    // 存在全局更新先停止
    if (window.global.updateReq) {
      window.global.updateReq.clientRequest.destroy();
    }
  } catch (e) {
    console.log("updateReqErr", e);
  }
  remote.Window.get().reload();
}

// 退出app
export function quitApp() {
  mainNW.App.closeAllWindows();
  mainNW.App.quit();
}

// 打开查看大图
export function openViewer(imgList, index, w, h, type) {
  let width = w || screen.width;
  let height = h || screen.height;
  // 设置全局查看大图信息
  emitMsg("msg", {type: "global", setGlobal: 1, info: {viewerImgObj: {index: index, list: imgList, width: width, height: height, type: type}}});
  // 打开子窗口
  emitMsg("msg", {
    type: "window", newWin: 1, name: "viewer", width: width, height: height, transparent: true, changePath: true,
    path: (process.env.NODE_ENV != "development" ? `/viewer.html` : `http://localhost:8888/viewer.html`),
  });
}

// 打开查看大图/视频
export function toViewerMethod(e, className, queryElm, saveFlag) {
  if (!className) {
    className = "msg-img";
  }
  if (!queryElm) {
    queryElm = document.body;
  }
  let target = e.currentTarget;
  if (!new RegExp(className).test(target.className)) {
    target = e.currentTarget.querySelector("." + className);
  }
  console.log("查看大图", target.src, target.dataset.src);
  let imgList = [];
  let thisIndex = -1;
  queryElm.querySelectorAll("." + className).forEach((item, index) => {
    if (thisIndex == -1 && target.isEqualNode(item)) {
      thisIndex = index;
    }
    let param = {src: item.dataset.local, dataSrc: item.dataset.src, ext: item.dataset.ext, size: item.dataset.size, saveFlag: saveFlag};
    // 查看大图如果是纯图片消息收藏字段调整
    if (item.dataset.img && item.dataset.idserver && item.dataset.idclient) {
      param.msg = {
        idServer: item.dataset.idserver,
        idClient: item.dataset.idclient,
        scene: item.dataset.scene,
        from: item.dataset.from,
        to: item.dataset.to,
        time: item.dataset.time,
        type: "image"
      }
      param.sessionInfo = remote.store.getters.getSessions({id: item.dataset.sessionid});
      param.isImg = true;
    }
    imgList.push(param);
  });
  openViewer(imgList, thisIndex, target.naturalWidth, target.naturalHeight);
}

// 点击图片重新加载
export function clickImg(e, item) {
  let thisElm = e.target;
  if (thisElm.src == location.origin + "/img/image_reload_min.png") {
    // 重新加载失败图片
    thisElm.setAttribute("src", item.value);
  } else {
    selElm(thisElm);
  }
}

// 图片加载失败
export function errorImg(type, e) {
  if (type == 1) {
    e.target.setAttribute("src", `/img/image_not_found_min.png`);
  } else {
    e.target.setAttribute("src", `/img/image_reload_min.png`);
  }
}

// 打开转发子窗口
export function openForward(msg) {
  // 设置全局查看转发消息对象
  emitMsg("msg", {type: "global", setGlobal: 1, info: {forwardMsg: deepClone(msg)}});
  // 打开转发窗口
  emitMsg("msg", {
    type: "window", newWin: 1, name: "forward", width: 600, height: 550, transparent: true, changePath: true, resizable: false,
    path: (process.env.NODE_ENV != "development" ? `/forward.html` : `http://localhost:8888/forward.html`),
  });
}

// 打开子窗口
export function openChildWin(name, param) {
  let width = 0;
  let height = 0;
  let transparent = false;
  let type = 0;// 1child子窗口 2自定义子窗口
  let winParam = {};
  switch (name) {
    case "netConnect":
      // 网络连接-老
      type = 1;
      width = 750;
      height = 680;
      break;
    case "netConnectNew":
      // 网络连接-新
      type = 1;
      width = 443;
      height = 144;
      break;
    case "netConnectTemp":
      // 临时电脑授权
      type = 1;
      width = 410;
      height = 245;
      break;
    case "netDetect":
      type = 2;
      winParam = {
        type: "window", newWin: 1, name: "netDetect", width: 720, height: 530, minWidth: 720, minHeight: 530, transparent: true, changePath: true, resizable: true, always_on_top: param?.always_on_top,
        path: (process.env.NODE_ENV != "development" ? `/netDetect.html` : `http://localhost:8888/netDetect.html`),
      }
      break;
    case "codeRecord":
      type = 1;
      width = 680;
      height = 530;
      break;
  }
  if (type == 1) {
    emitMsg("msg", {type: "window", newWin: 1, name: name, path: `child/${name}`, width: width, height: height, transparent: transparent});
  } else if (type == 2) {
    emitMsg("msg", winParam);
  }
}

// 上传七牛,key-七牛指定目录空间名
export function uploadToQiNiu(file, token, key) {
  let formData = new FormData();
  formData.append("token", token);
  formData.append("file", file);
  if (key) {
    formData.append("key", key);
  }

  return new Promise(function (resolve, reject) {
    let oReq = new XMLHttpRequest();
    oReq.open("POST", "http://upload.qiniu.com/", true);
    oReq.onreadystatechange = function (e) {
      if (oReq.readyState == 4 && oReq.status == 200) {
        resolve(JSON.parse(oReq.responseText));
      } else if (oReq.readyState == 4 && oReq.status !== 200) {
        reject(oReq.statusText);
      }
    };
    oReq.send(formData);
  });
}

// 是否有输入框内容
export function hasContent(content) {
  return content && content.ops && (content.ops.length > 1 || content.ops[0] && content.ops[0].insert != "\n");
}

// 判断群消息通知的对象
export function isNotificationRole(msg, isThisOwner, isTeamManager) {
  // 操作的成员是否存在当前用户
  let isAccount = false;
  if (msg.attach) {
    let {type, account, accounts, users} = msg.attach;
    if (users) {
      isAccount = msg.attach.users.some(function (item) {
        if (item.account == getUserInfo().workerNo) {
          return true;
        }
      });
    }
    if (type) {
      type = type.replace("SuperTeam", "Team");
      // 群成员变更只有小乐不显示通知
      switch (type) {
        case "addTeamMembers":
        case "removeTeamMembers":
          if (accounts && accounts.length == 1 && accounts[0] == config[config.env].robotEmpNo) {
            return false;
          }
          break;
        case "leaveTeam":
          if (users && users.length == 1 && users?.[0]?.account == config[config.env].robotEmpNo) {
            return false;
          }
          break;
        case "passTeamApply":
          if (account == config[config.env].robotEmpNo) {
            return false;
          }
          break;
      }
    }
  }
  if (isThisOwner || isTeamManager || isAccount || processingNotification(msg)) {
    return true;
  } else {
    return false;
  }
}

// 判断群通知类型是否通知用户,type为msg.attach.type,team为msg.attach.team群消息
export function processingNotification(msg) {
  let {type, team} = msg.attach;
  if (type) {
    type = type.replace("SuperTeam", "Team");
  }
  let showFlag = false;
  switch (type) {
    case 'addTeamMembers':
      break;
    case 'removeTeamMembers':
      break;
    case 'leaveTeam':
      break;
    case 'updateTeam':
      if (typeof team.announcement == "string") {
        try {
          team.announcement = JSON.parse(team.announcement);
        } catch (e) {
        }
      }
      // 修改群名称、头像、公告、信息
      if (team.name || team.avatar || team.announcement || team.serverCustom) {
        showFlag = true;
      }
      // 其他暂不显示 intro-群介绍 inviteMode-群邀请他人权限为管理员 beInviteMode-群被邀请他人权限为不需要验证 updateTeamMode-群资料修改权限为管理员或群资料修改权限为所有人
      break;
    case 'acceptTeamInvite':// 接受邀请
      break;
    case 'passTeamApply':// 通过要求
      break;
    case 'dismissTeam'://解散群
      showFlag = true;
      break;
    case 'updateTeamMute':// 禁言
      break;
    case 'addTeamManagers'://新增群管理员
      break;
    case 'removeTeamManagers'://移除群管理员
      break;
    case 'transferTeam'://移交群
      break;
    default:
      break;
  }
  return showFlag;
}

// 设置消息体字符串
export function setMsgString(msg) {
  // 对应字段转为字符串
  if (msg.custom && typeof msg.custom != "string") {
    msg.custom = JSON.stringify(msg.custom);
  }
  if (msg.content && typeof msg.content != "string") {
    msg.content = JSON.stringify(msg.content);
  }
  return msg;
}

// 获取str中的参数
export function getStrParam(str, name) {
  let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
  let r = str.substr(1).match(reg);  //匹配目标参数
  if (r != null) return unescape(r[2]);
  return null; //返回参数值
}

// 计算图片显示宽高
export function calcWH(minW, minH, maxW, maxH, w, h) {
  let radioW = w / maxW;
  let radioH = h / maxH;
  // 限制宽高重新设置
  if (radioW > 1 || radioH > 1) {
    if (radioW > radioH) {
      h = maxW / w * h;
      w = maxW;
    } else {
      w = maxH / h * w;
      h = maxH;
    }
  }
  // 最小宽高10*10
  if (w < minW) {
    w = minW;
  }
  if (h < minH) {
    h = minH;
  }
  return {w: w, h: h};
}

// 获取日志文件名
export function getLogFileInfo(type, no, date) {
  let fileName = "stdout";
  if (no || type == 3) {
    switch (String(type)) {
      case "1":
        // 工作日志
        fileName = "stdout";
        break;
      case "2":
        // 错误日志
        fileName = "stderr";
        break;
      case "3":
        // 操作时间日志
        fileName = "time";
        break;
      case "4":
        // 点击日志
        fileName = "click";
        break;
      case "5":
        // 云信websocket日志
        fileName = "ws";
        break;
      case "6":
        // 云信详细日志
        fileName = "detail";
        break;
      case "7":
        // 云信日志
        fileName = "nim";
        break;
      case "8":
        // 性能日志
        fileName = "load"
        break;
      case "9":
        // 网络日志
        fileName = "net"
        break
      case "10":
        // 代理日志
        fileName = "proxy"
        break;
      default:
        break;
    }
    if (type != 3) {
      fileName += "_" + no;
    }
  }
  return {path: getAppPath(`\\logs\\`), name: `${fileName}_${date}`};
}

// 是否客户咨询列表
export function isFcwList(sessionInfo, id, teamInfo, notShow, isNotShow) {
  let thisTeamInfo = {};
  let sessionType = "";
  if (sessionInfo) {
    sessionType = getSessionType(sessionInfo).type;
    thisTeamInfo = remote.store.state.teams[sessionInfo.to];
  } else if (new RegExp(config.fcw).test(id)) {
    sessionType = 5;
  } else if (teamInfo) {
    sessionType = getTeamType(teamInfo);
    thisTeamInfo = teamInfo;
  }
  // 进线客户讨论组(只有群主显示)、售后讨论组不显示
  let isNotShowFlag = false;
  if (notShow && ((sessionType == 12 && thisTeamInfo.owner != getUserInfo().workerNo) || sessionType == 14)) {
    sessionType = -1;
    isNotShowFlag = true;
  }
  // 返回是否不显示客户讨论组会话
  if (isNotShow) {
    return isNotShowFlag;
  }
  // 客户会话列表（客户、进线客户讨论组、售后讨论组、限制讨论组）
  return sessionType == 5 || sessionType == 12 || sessionType == 14 || sessionType == 15;
}

// 获取人员 info为6位数工号/数组
export function getPersons(state, info) {
  let isArray = true;
  if (!Array.isArray(info)) {
    isArray = false;
    info = [info];
  }
  let person = info.map(item => {
    if (state.persons[item]) {
      return state.persons[item]
    } else if (state.nimFriend[item]) {
      let nimFriend = {
        ...state.nimFriend[item],
      }
      delete nimFriend.detailInfo;
      nimFriend.detailInfo = deepClone(nimFriend);
      return nimFriend;
    } else {
      return {
        name: item,
        workerNo: item,
        avatar: "/img/default/p2p.png",
      }
    }
  });
  return isArray ? person : person[0];
}

// 处理消息体多余字段
export function dealMsgField(thisMsg) {
  try {
    if (thisMsg.custom) {
      try {
        if (typeof thisMsg.custom == "string") {
          thisMsg.custom = JSON.parse(thisMsg.custom);
        }
      } catch (e) {
      }
      // 去除引用
      if (thisMsg.custom.quoteTxt) {
        delete thisMsg.custom.quoteTxt;
        delete thisMsg.custom.serverID;
        delete thisMsg.custom.quoteType;
        delete thisMsg.custom.time;
        delete thisMsg.custom.quoteImageList;
        delete thisMsg.custom.quoteImageLength;
      }
      // 去除@人
      if (thisMsg.custom.hait) {
        delete thisMsg.custom.hait;
        delete thisMsg.custom.haitPosition;
        delete thisMsg.custom.atName;
      }
    }
    // 删除文件多余字段
    if (thisMsg.content) {
      try {
        if (typeof thisMsg.content == "string") {
          thisMsg.content = JSON.parse(thisMsg.content);
        }
      } catch (e) {
      }
      if (thisMsg.content.msgs && thisMsg.content.msgs.length > 0) {
        try {
          thisMsg.content.msgs.map(msgItem => {
            if (msgItem.file && msgItem.file.fileInfo) {
              dealFileField(msgItem);
            }
            // 去除@人
            if (msgItem.custom && (msgItem.custom.hait || msgItem.custom.haitPosition)) {
              delete msgItem.custom.hait;
              delete msgItem.custom.haitPosition;
              delete msgItem.custom.atName;
            }
            // 删除@文字缓存
            delete msgItem.showText;
          });
        } catch (e) {
        }
      }
    }
    // 对应字段转为字符串
    thisMsg = setMsgString(thisMsg);
    // 删除文件多余字段
    if (thisMsg.file && thisMsg.file.fileInfo) {
      dealFileField(thisMsg);
    }
  } catch (e) {
    console.log("dealMsgFieldErr", e);
  }
  return thisMsg;
}

// 处理文件多余字段
export function dealFileField(thisMsg) {
  if (thisMsg.file) {
    delete thisMsg.file.fileInfo;
    delete thisMsg.file.videoInfo;
    delete thisMsg.file.showName;
    delete thisMsg.file.fileMD5;
    delete thisMsg.file.showW;
    delete thisMsg.file.showH;
    delete thisMsg.file.downloadMD5;
    delete thisMsg.file.progress;
    delete thisMsg.file.isImage;
  }
}

// 保存src图片到本地
export function saveSrcToLocal(src) {
  return new Promise(async resolve => {
    let currWin = remote.Window.get();
    // 获取聚焦的窗口
    for (let key in window.global.childWin) {
      if (window.global.childWin[key] && window.global.childWin[key].isFocused) {
        currWin = window.global.childWin[key];
      }
    }
    selFolderDialog({defaultPath: `乐聊图片${Date.now()}.png`}).then(async selFilePath => {
      currWin.focus();
      // 不存在后缀设置为文件类型后缀
      if (!path.extname(selFilePath)) {
        selFilePath += ".png";
      }
      let img = await dataURLToImage(src);
      let base64 = getBase64Image(img);
      await saveBase64Local({path: selFilePath, base64: base64});
      resolve();
    });
  })
}

// 获取图片大小
export function getImageData(img) {
  return new Promise(async resolve => {
    let canvas = document.createElement('canvas');
    canvas.width = img.width;
    canvas.height = img.height;
    let context = canvas.getContext('2d');
    let imgWH = calcWH(10, 10, screen.width, screen.height, img.width, img.height);
    context.drawImage(img, 0, 0, imgWH.w, imgWH.h);
    let imageData = context.getImageData(0, 0, imgWH.w, imgWH.h);
    let imageBlob = await canvasToFile(canvas, "image/webp", 1);
    resolve({imageData: imageData, imageBlob: imageBlob});
  });
}

// 设置图片显示压缩路径
export function getImageQualityUrl(url, notChange, quality) {
  // 不是云信域名不转、查看大图不转
  if ((!config.easePicUrlReg.test(url) && !new RegExp(`^(${config.jjsPicUrl1}|${config.jjsPicUrl3})$`).test(url)) || notChange) {
    return url;
  }
  // 视频封面不需要压缩
  if (/vframe/.test(url)) {
    return url;
  }
  return url + (url.indexOf("?") > -1 ? "&" : "?") + `imageView&quality=${quality || 80}&interlace=1`;
}

// freeLogin跳转新系统免登录
export async function getFreeLoginUrl(url, frameName) {
  let host = "";
  try {
    host = new URL(url).host;
  } catch (e) {
  }
  if (/************|dev.leyoujia.com|test.leyoujia.com|itest-dify.leyoujia.com|i.leyoujia.com|coa.leyoujia.com/.test(host)) {
    if (/.leyoujia.com/.test(host)) {
      // 线下测试环境登录系统需要https环境
      url = url.replace("http://", "https://");
    }
    if (frameName == "login" || frameName == "jjsHome") {
      url = await getNewSysUrl(url);
    }
  }
  return url;
}

// 登录新系统跳转指定页面
export async function getNewSysUrl(url) {
  let res = await remote.store.dispatch("getLoginSecret", {url: url});
  if (!res.success && res.errorCode != 5000) {
    console.log("getLoginSecretApiErr", res);
    let alertParam = {
      content: res.errorMsg || "抱歉,登入新系统错误,请稍后再试哦!",
      showCancel: false,
    }
    // 私人电脑无权限访问新系统
    if (res.errorCode == 99996) {
      alertParam.showCancel = true;
      alertParam.okText = "申请临时权限";
      alertParam.done = (type) => {
        if (type == 1) {
          openChildWin('netConnectTemp');
        }
      }
    }
    alert(alertParam);
  } else if (res.data && res.data.secretKey) {
    let host = config[config.env].jjsHome;
    if (/.leyoujia.com/.test(host)) {
      // 线下测试环境登录系统需要https环境
      host = host.replace("http://", "https://");
    }
    return `${host}/jjslogin/forward?id=${res.data.secretKey}${url ? "&url=" + url : ""}`
  } else {
    alert({
      content: "电脑未认证，不能跳转新系统，请去乐聊-工作台-乐聊工具-公司电脑认证提交后再试。",
      okText: "去认证",
      done: type => {
        if (type == 1) {
          openChildWin("netConnectNew");
        }
      }
    });
  }
  return "";
}

// 关闭所有窗口
export function closeAllWin() {
  for (let key in window.global.childWin) {
    window.global.childWin[key].close();
    delete window.global.childWin[key];
  }
}

// 获取子窗口
export function getChildWin(name, id, isExit) {
  // 根据id返回
  let thisWin = "";
  let thisWinLoading = window.global.childWinLoadingMap[name];
  if (id) {
    thisWin = window.global.mainWin;
    for (let key in window.global.childWin) {
      if (window.global.childWin[key].cWindow && window.global.childWin[key].cWindow.id == id) {
        thisWin = window.global.childWin[key];
        break;
      }
    }
  } else if (name) {
    let currentWindow = "";
    if (window.global.childWin[name]) {
      try {
        if (window.global.childWin[name].window) {
          currentWindow = window.global.childWin[name];
        }
      } catch (e) {
        try {
          currentWindow = window.global.childWin[name].window.nw.Window.get();
          window.global.childWin[name] = currentWindow;
        } catch (e) {
          delete window.global.childWin[name];
        }
      }
    }
    if (currentWindow) {
      thisWin = currentWindow;
    }
  }
  if (thisWin) {
    setWinMethod(thisWin);
  }
  // 判断是否存在子窗口返回
  if (isExit) {
    return {isExit: thisWinLoading || thisWin, win: thisWin};
  }
  return thisWin;
}

// 设置窗口方法
export function setWinMethod(win) {
  win.getBounds = function () {
    return {
      x: win.x,
      y: win.y,
      width: win.width,
      height: win.height
    }
  }
  win.setBounds = function (param) {
    if (param.width) {
      win.width = param.width;
    }
    if (param.height) {
      win.height = param.height;
    }
    if (param.center) {
      win.setPosition("center");
    } else {
      win.x = param.x;
      win.y = param.y;
    }
  }
}

// 判断是否为主窗口
export function isMainWin(id) {
  return (id || nw.Window.get().cWindow.id) == remote.Window.get().cWindow.id;
}

// 解压文件
export function admZipFile(from, to) {
  let zip = new AdmZip(from);
  let currTime = new Date().getTime();
  zip.extractAllTo(to, true);
  console.log("AdmZip:", new Date().getTime() - currTime);
}

// 获取父元素
export function getParents(el, className) {
  let p = el;
  let flag = false;
  while (p != null && !flag) {
    if (p.classList) {
      for (let i = 0; i < p.classList.length; i++) {
        if (className == p.classList[i]) {
          flag = true;
          break;
        }
      }
    }
    if (!flag) {
      p = p.parentNode;
    }
  }
  return p;
}

// 跳转应用url
export function openAppUrl(thisUrl) {
  // 判断不存在域名加上
  try {
    new URL(thisUrl);
  } catch (e) {
    // 如果是链接机上前缀，如果是新系统相对路径则加上域名
    if (getLink("", true).test(thisUrl)) {
      linkFormat(thisUrl)
    } else {
      thisUrl = config[config.env].jjsHome + thisUrl;
    }
  }
  window.store.dispatch("setOpenWindow", [linkFormat(thisUrl), "login"]);
}

// 发送埋点
export function setJJSEvent(key, param) {
  try {
    remote.Window.get().window?._jjshome_t?._set_event(key, param);
  } catch (e) {
    console.log("setJJSEventErr", key, param, e);
  }
}

// 结束进程重启应用
export function reluanchApp() {
  window.global.reluanchApp();
}

// 获取/设置本地更新数据对象
export function setLocalUpdateObj(param = {}) {
  let updateObj = JSON.parse(localStorage.getItem("updateObj") || "{}");
  let serverVersion = remote.store.getters.getConfig.serverVersion;
  // 设置对应数据
  for (let key in param) {
    updateObj[key] = param[key];
  }
  if (!updateObj.version) {
    updateObj.version = serverVersion;
  }
  if (!updateObj.times) {
    updateObj.times = 0;
  }
  // 存在更高下载版本重置版本更新提示和下载失败次数
  if (compareVersion(updateObj.version, serverVersion)) {
    updateObj.version = serverVersion;
    updateObj.notTips = false;
    updateObj.times = 0;
  }
  setLocalStorage("updateObj", JSON.stringify(updateObj));
  return updateObj;
}

// 判断是否超过半年-超过半年不显示图片和不支持下载
export function isHalfYearAgo(time) {
  return new Date().getTime() + remote.store.getters.getDiffTime - time > 180 * 24 * 60 * 60 * 1000;
}

// 触发事件-type为触发事件如mouseup
export function dispatchEvent(type) {
  let event = document.createEvent("Event");
  event.initEvent(type, true, true);
  document.dispatchEvent(event);
}

// 获取默认会话类型
export function isDefaultSessions(item, sessionType, hasChildWin) {
  // 默认会话不返回房产网列表、群助手、订阅号
  if (isFcwList(item) || item.isHelper || (sessionType == 8 && item.to != config.subscribe) || hasChildWin) {
    return false;
  }
  return true;
}

// 判断是否存在当前会话子窗口
export function hasItemChildWin(info, item) {
  // 判断是否存在子窗口
  let hasChildWin = false;
  // 子窗口获取不屏蔽该会话
  if (!info.child && (info.sort == -1 || info.sort == 7 || info.count)) {
    let childWin = getChildWin("chat-" + item.id);
    if (remote.store.state.router.currentRoute.path == "/index/chat" && childWin) {
      hasChildWin = true;
    }
  }
  return hasChildWin;
}

// 获取用户佩戴勋章类型-1勋章2成就
export function userWearPic(userInfo) {
  userInfo = userInfo || {};
  return userInfo.wearType == 2 ? userInfo.mcUrl || "/img/medal_bg.png" : userInfo.pcMedalUrl || userInfo.medalUrl;
}

// 后台消息字段转换
export function convertMsg(msgs) {
  msgs = msgs || [];
  let isArray = true;
  if (!Array.isArray(msgs)) {
    isArray = false;
    msgs = [msgs];
  }
  let returnMsg = [];
  for (let i = 0; i < msgs.length; i++) {
    let thisMsgObj = msgs[i];
    let thisMsg = thisMsgObj.msgJson || thisMsgObj.content || thisMsgObj;
    let thisItem = {
      time: Number(thisMsg.msgTimestamp),
      text: thisMsg.body,
      idServer: thisMsg.msgidServer,
      idClient: thisMsg.msgidClient,
      from: thisMsg.fromAccount,
      to: thisMsg.to,
      scene: thisMsg.convType == "SUPER_TEAM" || thisMsg.convType == "superteam" ? "superTeam" : thisMsg.convType == "TEAM" || thisMsg.convType == "team" ? "team" : "p2p",
      status: "success",
      collectType: thisMsgObj.type,
      sourceType: thisMsgObj.sourceType,
      sourceName: thisMsgObj.sourceName,
      sourceIcon: thisMsgObj.sourceIcon,
      sourceId: thisMsgObj.sourceId,
      isServerMsg: true,
    }
    let thisType = thisMsg.msgType.toLowerCase();
    // 后台通知消息字段太杂乱暂不处理
    if (thisType == "notification") {
      msgs.splice(i, 1);
      i--;
      continue;
    }
    switch (thisType) {
      case "picture":
        thisItem.type = "image";
        thisItem.file = thisMsg.attach;
        break;
      case "location":
        thisItem.type = "geo";
        thisItem.geo = thisMsg.attach;
        break;
      case "notification":
        if (thisMsg.attach) {
          thisItem.type = thisType;
          thisItem.attach = thisMsg.attach;
        }
        break;
      default:
        thisItem.type = thisType;
        switch (thisType) {
          case "file":
          case "audio":
          case "video":
            if (thisMsg.attach) {
              thisItem.file = thisMsg.attach;
            }
            break;
          default:
            if (thisMsg.attach) {
              thisItem.content = thisMsg.attach;
            }
            break;
        }
        break
    }
    try {
      // 兼容历史数据
      if (thisItem.file) {
        thisItem.file = JSON.parse(thisItem.file);
      } else if (thisItem.content) {
        try {
          thisItem.content = JSON.parse(thisItem.content);
        } catch (e) {}
      } else if (thisItem.geo) {
        try {
          thisItem.geo = JSON.parse(thisItem.geo);
        } catch (e) {}
      }
    } catch (e) {}
    if (thisItem.type == "file" && config.imgTypeReg.test(thisItem.file?.ext)) {
      thisItem.file.isImage = true;
    }
    returnMsg.push(thisItem);
  }
  return isArray ? returnMsg : returnMsg[0];
}

// 收藏后台消息字段转换
export function convertCollectMsg(msgs) {
  msgs = msgs || [];
  let isArray = true;
  if (!Array.isArray(msgs)) {
    isArray = false;
    msgs = [msgs];
  }
  let returnMsg = [];
  msgs.map(thisMsg => {
    let newItem = {};
    // 兼容历史文件、图文消息数据
    try {
      if (((thisMsg.type == "file" || thisMsg.type == "else") && !thisMsg.msgJson && thisMsg.oHtml) || thisMsg.content) {
        let thisAttach = {};
        if (thisMsg.content) {
          thisAttach = thisMsg.content;
          try {
            thisAttach = JSON.parse(thisAttach);
          } catch (e) {}
        } else if (thisMsg.type == "file") {
          // oHtml
          let node = document.createElement("div");
          node.innerHTML = thisMsg.oHtml;
          let fileElm = node.querySelector(".shareFile");
          // 去除元素加载属性
          let imgElms = node.querySelectorAll("img");
          for (let i = 0; i < imgElms.length; i++) {
            imgElms[i].removeAttribute("onload");
            imgElms[i].removeAttribute("onerror");
          }
          if (fileElm) {
            thisAttach.ext = fileElm.getAttribute("data-file-ext");
            thisAttach.size = fileElm.getAttribute("data-total-size");
            thisAttach.name = fileElm.getAttribute("data-file-name");
            thisAttach.url = fileElm.getAttribute("data-file-url");
          }
        }

        thisMsg.msgJson = {
          attach: thisAttach,
          msgidServer: thisMsg.msgIdServer,
          msgidClient: thisMsg.msgIdClient,
          convType: thisMsg.msgScene,
          fromAccount: thisMsg.empNo,
          to: thisMsg.msgTo,
          msgTimestamp: thisMsg.msgTime,
          msgType: thisMsg.type
        }
      }
    } catch (e) {
      console.log("getCollectFileErr", e)
    }
    // 兼容历史数据
    if ((thisMsg.msgJson && thisMsg.msgJson.attach && Object.keys(thisMsg.msgJson.attach).length > 0) || thisMsg.content) {
      newItem = convertMsg(thisMsg);
    } else {
      newItem = {
        collectId: thisMsg.id,
        time: new Date(thisMsg.updateTime).getTime(),
        text: (thisMsg.oText || "").replace(/&nbsp;/g, " "),
        oHtml: (thisMsg.oHtml || "").replace(/&nbsp;/g, " "),
        idServer: thisMsg.msgIdServer,
        idClient: thisMsg.msgIdClient,
        from: thisMsg.sourceId,
        to: thisMsg.msgTo,
        scene: thisMsg.msgScene,
        type: thisMsg.type == "note" ? "collectNote" : thisMsg.type == "else" ? "collectElse" : thisMsg.type,
        collectType: thisMsg.type,
        sourceType: thisMsg.sourceType,
        sourceName: thisMsg.sourceName,
        sourceIcon: thisMsg.sourceIcon,
        sourceId: thisMsg.sourceId,
        status: "success",
        isServerMsg: true,
      }
      // 无法识别的历史数据
      if (thisMsg.type != "text" && thisMsg.type != "else" && thisMsg.type != "note") {
        newItem.type = "collectError";
        newItem.text = "";
        newItem.oHtml = "";
      }
      // 替换图片中的点击和加载事件
      if ((newItem.type == "collectNote" || newItem.type == "collectElse") && /img/.test(newItem.oHtml)) {
        let node = document.createElement("div");
        node.innerHTML = thisMsg.oHtml;
        let imgElms = node.querySelectorAll("img");
        for (let i = 0; i < imgElms.length; i++) {
          let imgElm = imgElms[i];
          imgElm.removeAttribute("onclick");
          imgElm.removeAttribute("dblclick");
          imgElm.removeAttribute("onload");
          imgElm.removeAttribute("onerror");
          if ((/\/app\/images\//).test(imgElm.src)) {
            // 表情替换
            imgElm.outerHTML = imgElm.getAttribute("data-text");
          } else {
            // 图片加载失败切换天翼云
            imgElm.setAttribute("onerror", `
              if (${config.easePicUrlOtherReg}.test(this.src)) {
                this.src = this.src.replace(${config.easePicUrlOtherReg}, "${config.jjsPicUrlOther}").split("&")[0];
              } else {
                this.src = "/img/fang_default.png";
                this.removeAttribute("onerror");
              }
            `);
          }
        }
        newItem.oHtml = node.innerHTML;
      }
    }
    // 存在新消息体的才渲染
    if (Object.keys(newItem).length > 0) {
      newItem.sourceName = thisMsg.sourceName;
      newItem.updateTime = thisMsg.updateTime;
      newItem.collectId = thisMsg.id;
      newItem.parentId = thisMsg.groupId;
      newItem.isServerMsg = true;
      returnMsg.push(newItem);
    }
  });
  return isArray ? returnMsg : returnMsg[0];
}

// 遍历元素 insertType-1插入-2查看-3转发
export function loadElmTree(insertType, parent, callback) {
  for (let i = 0; i < parent.childNodes.length; i++) {
    // 遍历第一级子元素
    let child = parent.childNodes[i];
    let className = "";
    if (child.attributes) {
      className = child.attributes.getNamedItem("class");
    }
    // 禁止复制、只复制文本和复制@人员不继续遍历子元素
    if (className && className.nodeValue) {
      // 复制文本
      if (/dataCopy/.test(className.nodeValue)) {
        let divNode = document.createElement("div");
        divNode.innerText = child.attributes.getNamedItem("data-copy").value;
        callback({type: "text", text: divNode.innerText}, divNode);
        continue;
      }
      if (/notCopy/.test(className.nodeValue)) {
        continue;
      }
      // @人员
      let haitAccount = child.attributes.getNamedItem("data-hait-account") || child.attributes.getNamedItem("data-@-account");
      if (child.nodeName == "IMG" && haitAccount && !/reEditor/.test(className.nodeValue)) {
        let thisUserInfo = remote.store.getters.getPersons(haitAccount.value);
        let thisUserName = thisUserInfo ? thisUserInfo.name : haitAccount.value;
        if (thisUserName == "all") {
          thisUserName = "全员";
        }
        let spanNode = document.createElement("span");
        spanNode.innerText = "@" + thisUserName;
        callback({type: "text", text: spanNode.innerText}, spanNode);
        continue;
      }
    }
    // 返回对应图片和文本内容
    if ((child.nodeName == "#text" && child.textContent) || child.nodeName == "SCRIPT") {
      let spanNode = document.createElement("span");
      let showText = "";
      if (insertType == 1 || insertType == 2) {
        showText = htmlEscapeAll(child.textContent);
      } else {
        showText = child.textContent;
      }
      if (child.nodeName == "SCRIPT") {
        showText = child.outerHTML;
        if (insertType == 1) {
          showText = htmlEscapeAll(spanNode.innerText);
        }
      }
      spanNode.innerText = showText.replace(/\n/g, "<br>");
      if (isLine(child)) {
        callback({type: "text", text: "\n"}, document.createElement("br"));
      }
      callback({type: "text", text: showText}, spanNode);
      continue;
    } else if (child.nodeName == "BR") {
      if (isLine(child)) {
        callback({type: "text", text: "\n"}, document.createElement("br"));
      }
      callback({type: "text", text: "\n"}, child);
    } else if (child.nodeName == "IMG") {
      let dataInfo = JSON.parse(child.dataset.info || "{}");
      if (isLine(child)) {
        callback({type: "text", text: "\n"}, document.createElement("br"));
      }
      callback({type: dataInfo.docId ? "document" : "image", src: child.dataset.src || child.src, ext: child.dataset.ext, file: dataInfo}, child);
      continue;
    }
    // 递归调用
    loadElmTree(insertType, child, callback);
  }
}

// 判断元素是否换行
export function isLine(child) {
  return child.childNodes.length == 0 && child.parentNode && !(child.parentNode.childNodes.length == 1 && child.nodeName == "BR") && child.parentNode.childNodes[0] == child && child.parentNode.contentEditable != "true" && ['block', 'list-item'].indexOf(window.getComputedStyle(child.parentNode).display) > -1
}

// 消息转html内容 insertType-1插入-2查看-3转发
export function msgToHtml(msg, insertType) {
  let text = "";
  let thisText = "";
  switch (msg.type) {
    case "text":
      msg.notSelf = true;
      thisText = msg.text;
      text = strToHtml(thisText, "", msg, "", true);
      break;
    case "image":
      let dataInfo = deepClone(msg);
      dealFileField(dataInfo);
      if (dataInfo.file.name) {
        dataInfo.file.name = dataInfo.file.name.replace(/\s/, "_");
      }
      text = `<img data-ext="${dataInfo.file.ext}" data-info=${JSON.stringify(dataInfo.file)} src="${dataInfo.file.url}">`;
      break;
    case "custom":
      if (msg.content && msg.content.type == "multi" && msg.content.msgs && msg.content.msgs.length > 0) {
        msg.content.msgs.map(item => {
          if (item.type == "text" || item.type == "document") {
            item.notSelf = true;
            let notLink = item.type == "document" ? true : false;
            thisText = item.text;
            text += strToHtml(thisText, notLink, item, "", true);
          } else if (item.type == "image") {
            let dataInfo = deepClone(item);
            dealFileField(dataInfo);
            if (dataInfo.file.name) {
              dataInfo.file.name = dataInfo.file.name.replace(/\s/, "_");
            }
            text += `<img data-ext="${dataInfo.file.ext}" data-info=${JSON.stringify(dataInfo.file)} src="${item.file.url}">`;
          }
        });
      }
      break;
    case "collectNote":
      text += htmlUnEscapeAll(msg.oHtml);
      break;
    case "collectElse":
      text += thisText;
      break;
  }
  return text;
}

// html元素转消息 insertType-1插入-2查看-3转发-4笔记 editType-1为另存为不需要重新上传图片-2笔记需要重新上传
export function elmToMsg(insertType, elm, editType) {
  return new Promise(resolve => {
    let msgs = [];
    let html = "";
    let thisHtml = "";
    let detailMsgs = [];
    let previewerP = [];
    let index = 0;
    loadElmTree(insertType, elm, (obj, elm) => {
      switch (elm.nodeName) {
        case "IMG":
          if (obj.type == "document") {
            // 乐文档
            thisHtml = `乐文档`;
            msgs.push({type: obj.type, file: obj.file});
            detailMsgs.push({type: obj.type, file: obj.file, html: thisHtml});
            html += thisHtml;
          } else if (new RegExp(location.origin).test(obj.src)) {
            // 表情解析
            thisHtml = getEmojiText(obj.src.slice(obj.src.lastIndexOf("/") + 1));
            if (thisHtml) {
              if (msgs.length > 0 && msgs[msgs.length - 1].type == "text") {
                // 上一个消息是文本追加消息体
                msgs[msgs.length - 1].text += thisHtml;
                detailMsgs[detailMsgs.length - 1].text += thisHtml;
                detailMsgs[detailMsgs.length - 1].html += thisHtml;
              } else {
                msgs.push({type: "text", text: thisHtml});
                detailMsgs.push({type: "text", text: thisHtml, html: thisHtml});
              }
              html += thisHtml;
            }
          } else {
            let thisBse64 = obj.src;
            let previewFileParam = {
              type: "image",
              name: Date.now() + `${index}`,
              dataURL: thisBse64,
              file: {
                url: thisBse64,
                ext: obj.ext
              },
              elm: elm
            }
            if (obj.file) {
              previewFileParam.file = {
                ...obj.file,
                url: thisBse64,
              }
            }
            index++;
            if (insertType == 3 || editType == 2) {
              // 转发才上传图片
              if (!/data:image\//.test(thisBse64)) {
                if (editType == 1) {
                  // 另存为返回图片
                  obj.file.url = obj.src;
                  thisHtml = `<img src="${thisBse64}">`;
                  msgs.push(obj);
                  detailMsgs.push({...obj, html: thisHtml});
                  html += thisHtml;
                  return;
                }
                // 图片地址失效无法转换-尝试判断天翼云地址是否存在
                previewerP.push(new Promise(async resolve => {
                  let thisSrc = obj.src;
                  let thisFile = {...previewFileParam};
                  // 兼容不是file前缀的本地图片
                  let isFile = /file:\/\/\//.test(thisSrc);
                  try {
                    if (fs.statSync(thisSrc)) {
                      isFile = true;
                    }
                  } catch (e) {
                    isFile = false;
                  }
                  if (isFile) {
                    // 本地压缩图片替换
                    if (new RegExp(`/cached/${getUserInfo().workerNo}/images/thum/`).test(thisSrc)) {
                      thisSrc = thisSrc.replace("/thum", "")
                    }
                    // 去除前后缀
                    thisSrc = thisSrc.split("?")[0].replace("file:///", "");
                    thisSrc = decodeURIComponent(thisSrc);
                    previewFileParam.dataURL = await getLocalFile({filePath: thisSrc, base64: true});
                    let thisFileObj = await remote.store.state.nimSDK.previewFile(previewFileParam);
                    thisFile = thisFileObj;
                    resolve(thisFile);
                  } else {
                    if (!await remote.store.dispatch("getHasJjsPicUrl", elm.src)) {
                      // 不存在图片-判断天翼云文件是否存在
                      let jjsPicInfo = await remote.store.dispatch("getHasJjsPicUrlOther", obj.src);
                      if (jjsPicInfo.jjsPicUrlOrigin) {
                        thisSrc = jjsPicInfo.url;
                      } else {
                        thisFile.error = true;
                      }
                    }
                    if (!thisFile.error) {
                      // 缓存图片到本地
                      downloadFile({
                        url: thisSrc,
                        name: Date.now() + "_" + uniqueSignInput,
                        path: getFileCachedPath({type: 1, account: getUserInfo().workerNo}),
                        ext: obj.ext || "png",
                        done: async (res) => {
                          if (res.state == "success" || res.state == "error") {
                            if (res.state == "success") {
                              previewFileParam.dataURL = await getLocalFile({filePath: res.path + res.name, base64: true});
                              let thisFileObj = await remote.store.state.nimSDK.previewFile(previewFileParam);
                              thisFile = thisFileObj;
                            } else {
                              thisFile.error = true;
                            }
                            resolve(thisFile);
                          }
                        }
                      });
                    } else {
                      resolve(thisFile);
                    }
                  }
                }));
              } else {
                previewerP.push(remote.store.state.nimSDK.previewFile(previewFileParam));
              }
            } else {
              previewerP.push(new Promise(resolve => {
                resolve({type: "image", file: {url: previewFileParam.dataURL, name: previewFileParam.name}});
              }));
            }
            thisHtml = `$replaceText$replaceText-${previewFileParam.name}`;
            msgs.push(previewFileParam);
            detailMsgs.push({...previewFileParam, html: thisHtml})
            html += thisHtml;
          }
          break;
        default :
          thisHtml = obj.text;
          if (msgs.length > 0 && msgs[msgs.length - 1].type == "text") {
            // 上一个消息是文本追加消息体
            msgs[msgs.length - 1].text += obj.text;
            detailMsgs[detailMsgs.length - 1].text += obj.text;
            detailMsgs[detailMsgs.length - 1].html += thisHtml;
          } else {
            msgs.push(obj);
            detailMsgs.push({...obj, html: thisHtml});
          }
          html += thisHtml;
          break;
      }
    });
    Promise.allSettled(previewerP).then((data) => {
      let imgStatus = true;
      let errorMsg = "";
      for (let i = 0; i < data.length; i++) {
        if (data[i].status != "fulfilled" || data[i].value.error) {
          // 天翼云地址也不存在的时候删除图片
          if (data[i].status != "fulfilled") {
            imgStatus = false;
            errorMsg = "上传失败，请重新尝试";
          }
          // 删除图片
          for (let j = 0; j < msgs.length; j++) {
            if (msgs[j].type == "image" && msgs[j].name == data[i].value.name) {
              msgs[j].elm.src = data[i].value.dataURL;
              msgs.splice(j, 1);
              detailMsgs.splice(j, 1);
              break;
            }
          }
          html = html.replace(`$replaceText$replaceText-${data[i].value.name}`, "");
        } else {
          thisHtml = `<img data-ext="${data[i].value.file.ext}" data-info=${JSON.stringify(data[i].value.file)} src="${data[i].value.file.url}">`
          // 替换图片地址
          for (let j = 0; j < msgs.length; j++) {
            if (msgs[j].type == "image" && msgs[j].name == data[i].value.name) {
              msgs[j].elm.src = data[i].value.file.url;
              detailMsgs[j].elm.src = data[i].value.file.url;
              msgs[j].elm.dataset.ext = data[i].value.file.ext;
              detailMsgs[j].elm.dataset.ext = data[i].value.file.ext;
              msgs[j].elm.dataset.info = JSON.stringify(data[i].value.file);
              detailMsgs[j].elm.dataset.info = JSON.stringify(data[i].value.file);
              msgs.splice(j, 1, {type: "image", file: data[i].value.file});
              detailMsgs.splice(j, 1, {type: "image", file: data[i].value.file, html: thisHtml});
              break;
            }
          }
          html = html.replace(`$replaceText$replaceText-${data[i].value.name}`, thisHtml);
        }
      }
      resolve({success: imgStatus, errorMsg: errorMsg, msgs: msgs, html: html, detailMsgs: detailMsgs});
    });
  });
}

// 转发收藏消息 insertType-1插入-2查看-3转发
export function forwardCollectMsgs(list, insertType, sessionInfo) {
  return new Promise(async resolve => {
    let errorMsg = "";
    let thisMsg = {
      type: "custom",
      detailType: "collectMsg",
      pushContent: "[收藏消息批量转发]",
      forwardTitle: "[收藏消息批量转发]",
      forwardType: "item",
    };
    let thisMsgs = [];
    let p = [];
    list.map(item => {
      p.push(new Promise(async resolve => {
        let thisItem = {
          ...item
        };
        if (!thisItem.idClient) {
          thisItem.idClient = item.time + "-" + item.collectId;
        }
        let thisType = item.type;
        let thisInsertType = insertType;
        // 存在历史图片数据天翼云地址需要重新上传
        if (item.type == "custom" && item.oHtml && (insertType == 1 || insertType == 3)) {
          thisType = "else";
          thisInsertType = 3;
        }
        switch (thisType) {
          case "text":
            thisItem.text = item.text || item.oHtml;
            thisMsgs.push(thisItem);
            break;
          case "image":
          case "file":
          case "audio":
          case "video":
            if (item.file) {
              thisItem.file = item.file;
              thisMsgs.push(thisItem);
            }
            break;
          case "custom":
            if (item.content) {
              thisItem.content = item.content;
              thisMsgs.push(thisItem);
            }
            break;
          case "geo":
            if (item.geo) {
              thisItem.geo = item.geo;
              thisMsgs.push(thisItem);
            }
            break;
          default:
            let node = document.createElement("div");
            if (item.speechContent) {
              node.innerHTML = htmlEscapeAll(item.speechContent || "");
            } else {
              let thisHtml = item.oHtml || "";
              node.innerHTML = thisHtml;
            }
            let res = {};
            if (item.type == "collectNote" || item.collectType == "note") {
              // 收藏类型数据html需要转义
              res = await elmToMsg(4, node, thisInsertType == 3 ? 2 : "");
            } else {
              res = await elmToMsg(thisInsertType, node);
            }
            errorMsg = res.errorMsg;
            if (res.msgs.length > 0) {
              thisItem.content = {type: "multi", msgs: res.msgs};
              thisItem.type = "custom";
              thisMsgs.push(thisItem);
            }
            break;
        }
        resolve();
      }));
    });
    await Promise.all(p);
    if (errorMsg) {
      resolve({success: false, errorMsg: errorMsg});
      return;
    }
    thisMsg.multipleList = thisMsgs;
    if (thisMsgs.length > 0) {
      if (thisMsgs.length == 1) {
        thisMsg = thisMsgs[0];
      }
      if (insertType == 3 && !(sessionInfo && sessionInfo.to && thisMsgs.length == 1)) {
        openForward(thisMsg);
      }
    }
    resolve({success: true, thisMsg: thisMsg, thisMsgs: thisMsgs});
  })
}

// 滚动到可视区域 scrollBox滚动元素 selector判断元素类名 key-1上-2下-默认已经切换 func不存在元素执行方法
export function scrollLi(scrollBox, selector, key, func) {
  if (scrollBox) {
    let currLiElm = Array.prototype.find.call(scrollBox.querySelectorAll(selector), item => {return /curr/.test(item.className)});
    // 判断可视区域
    if (currLiElm) {
      let scrollBoxOffset = getOffset(scrollBox);
      let currLiOffset = getOffset(currLiElm);
      let changeHeight = key == 1 ? -currLiElm.offsetHeight : key == 2 ? currLiElm.offsetHeight : 0;
      if (currLiOffset.top + currLiElm.offsetHeight - scrollBoxOffset.top + changeHeight > scrollBox.scrollTop + scrollBox.clientHeight) {
        scrollBox.scrollTop = currLiOffset.top + currLiElm.offsetHeight + changeHeight - scrollBoxOffset.top - scrollBox.clientHeight;
      } else if (currLiOffset.top - scrollBoxOffset.top + changeHeight < scrollBox.scrollTop) {
        scrollBox.scrollTop = currLiOffset.top - scrollBoxOffset.top + changeHeight;
      }
    } else {
      func && func();
    }
  }
}

// 删除目录下的文件 thisPath-删除目录 time删除时间范围
export function deleteFile(thisPath, time) {
  fs.readdir(thisPath, function (err, menu) {
    if (!menu) return;
    menu.forEach(function (ele) {
      let filePath = thisPath + ele;
      fs.stat(filePath, function (err, info) {
        if (info.isDirectory()) return;
        // info.mtime文件的创建时间，文件超过7天没有修改，直接删除
        if (time) {
          if ((new Date().getTime() - new Date(info.mtime).getTime()) > time) {
            fs.unlink(filePath, function (err) { });
          }
        } else {
          fs.unlink(filePath, function (err) { });
        }
      });
    });
  });
}

// 订阅发布
export function Events() {
  return {
    events: {},
    emit: function (type, param) {
      let listeners = this.events[type] || [];
      for (const listener of listeners) {
        listener(param);
      }
    },
    on: function (type, listener) {
      this.events[type] = this.events[type] || [];
      this.events[type].push(listener);
    }
  }
}

// 设置用户本地缓存和获取,type-1设置2获取，item-key、value
export function userLocalStorage(item, type) {
  try {
    let settingLocalStorage = JSON.parse(localStorage.getItem(item.key) || "{}");
    let workerNo = getUserInfo().workerNo;
    if (type == 1) {
      settingLocalStorage[workerNo] = item.value;
      localStorage.setItem(item.key, JSON.stringify(settingLocalStorage));
    } else {
      return settingLocalStorage[workerNo] != undefined ? settingLocalStorage[workerNo] : (item.value != undefined ? item.value : {});
    }
  } catch (e) {
    return {};
  }
}

// 设置用户本地缓存和获取,type-1获取2设置，item-key、value
export async function userCache(item, type) {
  let workerNo = getUserInfo().workerNo;
  // @消息和特别关心消息迁移到indexedDB
  try {
    let settingCache = await remote.store.state.cacheDB.query(item.key);
    if (type == 1) {
      settingCache[workerNo] = item.value;
      await remote.store.state.cacheDB.update(item.key, JSON.stringify(settingCache));
    } else {
      return settingCache[workerNo] || item.value || {};
    }
  } catch (e) {
    return {};
  }
}

// 设置本地缓存
export function setLocalStorage(key, value) {
  try {
    localStorage.setItem(key, value);
  } catch (e) {
    console.log("setLocalStorageErr", key);
  }
}

// 设置消息缓存超过15天自动清理
export function removeMsgByTime(haitMsgMap, diffTime) {
  // 删除超过15天的@消息
  let localMsgTime = 15 * 24 * 60 * 60 * 1000;
  for (let pKey in haitMsgMap) {
    for (let idKey in haitMsgMap[pKey]) {
      let idItem = haitMsgMap[pKey][idKey];
      let hasMsg = false;
      for (let key in idItem) {
        if (idItem[key]?.time) {
          // 存在消息体判断是否删除
          if (Date.now() + diffTime - idItem[key].time > localMsgTime) {
            delete haitMsgMap[pKey][key];
          } else {
            hasMsg = true;
          }
        }
      }
      // 删除后不存在消息则删除对应会话的消息
      if (!hasMsg) {
        delete haitMsgMap[pKey][idKey];
      }
    }
  }
}

// 设置会话在线和已读/未读字段
export function setSessionField(item) {
  // 判断用户是否在线
  if (item.scene == "p2p" && (new RegExp(config.fcw).test(item.to) || getUserInfo().workerNo == "346765") && remote.store.state.fcwOnlineMap[item.to]) {
    item.userOnline = true;
  } else if (item.userOnline) {
    delete item.userOnline;
  }
  // 判断是否显示客户咨询最后一条公司员工发的消息已读/未读
  if (new RegExp(config.fcw).test(item.to)) {
    let lastMsgFrom = item.showLastFrom || item.lastMsg?.from;
    let showLastTime = item.showLastTime || item.lastMsg?.time;
    if (lastMsgFrom == getUserInfo().workerNo) {
      if (showLastTime > item.msgReceiptTime || !item.msgReceiptTime) {
        item.fcwShowUnread = 1;
      } else {
        item.fcwShowUnread = 2;
      }
    } else {
      delete item.fcwShowUnread;
    }
  }
}

// 获取是否在远程
export function getRemoteStatus() {
  return new Promise(getResolve => {
    console.time("getRemoteStatus");
    let flag = false;
    let p = [];
    let encoding = "gbk";
    let binaryEncoding = "binary";
    let data, dataList;
    try {
      for (let i = 0; i < remote.store.state.remoteCMDList.length; i++) {
        let item = remote.store.state.remoteCMDList[i];
        switch (item.type) {
          case "port":
            // 根据端口获取
            p.push(
              new Promise(resolve => {
                cp.exec(`netstat -an | findstr ${item.port} | find "ESTABLISHED"`, {encoding: binaryEncoding}, function (err, stdout, stderr) {
                  data = getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding);
                  dataList = trimArray(data.trim().replace(/\n/g, "~~").split("~~"));
                  if (dataList.length > 0) {
                    console.log("getRemoteStatus", item.type, item.port);
                    flag = true;
                  }
                  resolve();
                });
              })
            );
            break;
          case "task":
            // 根据任务列表获取
            p.push(
              new Promise(resolve => {
                cp.exec(`tasklist /FI "IMAGENAME eq ${item.taskName}"`, {encoding: binaryEncoding}, function (err, stdout, stderr) {
                  data = getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding);
                  dataList = trimArray(data.trim().replace(/\n/g, "~~").split("~~"));
                  // 存在两行描述
                  if (dataList.length > 2 + item.count) {
                    console.log("getRemoteStatus", item.type, item.taskName, item.count);
                    flag = true;
                  }
                  resolve();
                });
              })
            );
            break;
        }
      }
    } catch (e) {}
    Promise.all(p).then(res => {
      console.timeEnd("getRemoteStatus");
      getResolve(flag);
    });
  });
}

// 获取地址栏参数
export function getUrlParams(name, search = location.search) {
  return (new URLSearchParams(search)).get(name);
}

// 获取引用消息
export function getQuoteMsg(item, type) {
  let thisItem = deepClone(item);
  let quoteTxt = "";
  let quoteSendTxt = "";
  let quoteType = "";
  let quoteImageList = [];
  let quoteImageLength = 0;
  if (thisItem.type == "text") {
    quoteTxt = thisItem.text;
    quoteSendTxt = thisItem.text;
    quoteType = "text";
  } else if (thisItem.type == "file") {
    quoteTxt = '[文件]' + thisItem.file.name;
    quoteSendTxt = '[文件]' + thisItem.file.name;
    quoteType = "file";
  } else if (thisItem.type == "image") {
    quoteTxt = '[图片]';
    quoteImageLength++;
    dealFileField(thisItem);
    quoteImageList.push(thisItem.file);
    quoteType = "image";
  } else if (thisItem.type == "custom" && thisItem.content && thisItem.content.type == "multi") {
    let multiMsg = thisItem.content.msgs;
    quoteType = "imageText";
    for (let i = 0; i < multiMsg.length; i++) {
      if (multiMsg[i].type == "text") {
        if (multiMsg[i].text && multiMsg[i].text.trim() != "") {
          quoteTxt += multiMsg[i].text;
          quoteSendTxt += multiMsg[i].text + ",";
        }
      } else if (multiMsg[i].type == "image") {
        quoteTxt += "[图片]";
        quoteImageLength++;
        if (quoteImageList.length < 1) {
          dealFileField(multiMsg[i]);
          quoteImageList.push(multiMsg[i].file);
        }
      } else if (multiMsg[i].type == "document") {
        quoteTxt += "[乐文档]";
        quoteSendTxt += "[乐文档],";
      }
    }
    quoteSendTxt = quoteSendTxt.slice(0, -1);
  } else {
    quoteTxt = store.getters.getPrimaryMsg({msg: thisItem, primaryType: 4});
    quoteSendTxt = quoteTxt;
  }
  let msgUserInfo = remote.store.getters.getPersons(thisItem.from);
  let deptName = msgUserInfo.deptName ? msgUserInfo.deptName : "";
  quoteTxt = thisItem.fromNick + (deptName ? "-" + deptName : "") + "：" + quoteTxt;
  quoteSendTxt = thisItem.fromNick + (deptName ? "-" + deptName : "") + "：" + quoteSendTxt;
  if (quoteTxt.length > 200) {
    quoteTxt = quoteTxt.slice(0, 200);
  }
  if (quoteSendTxt.length > 200) {
    quoteSendTxt = quoteSendTxt.slice(0, 200);
  }
  let quoteMsg = {
    quoteTxt: quoteTxt,
    quoteSendTxt: quoteSendTxt,
    quoteType: quoteType,
    quoteImageList: quoteImageList,
    quoteImageLength: quoteImageLength,
    msg: thisItem
  }
  // 直接返回引用消息体
  if (type == 1) {
    return getMsgCustom({quoteMsg: quoteMsg});
  }
  return quoteMsg;
}

// 获取缓存占用空间大小
export function calcStorageSize() {
  const userDataPath = nw.App.dataPath;
  const indexedDBPath = path.join(userDataPath, 'IndexedDB');
  // 使用示例
  let storageSize = getDirectorySize(indexedDBPath);
  console.log("calcStorageSize", dealMem(storageSize));
  return storageSize;
}

// 计算目录总大小函数
export function getDirectorySize(dirPath) {
  let totalBytes = 0;
  let fileInfoList = [];

  function traverse(currentPath) {
    const stats = fs.statSync(currentPath);
    if (stats.isDirectory()) {
      const files = fs.readdirSync(currentPath);
      files.forEach(file => {
        traverse(path.join(currentPath, file));
      });
    } else if (stats.isFile()) {
      totalBytes += stats.size;
      fileInfoList.push(`${dealMem(stats.size)}`);
    }
  }

  try {
    traverse(dirPath);
    console.log("getDirectorySize", fileInfoList);
    return totalBytes;
  } catch (error) {
    console.log('计算目录大小失败:', error);
    return 0;
  }
}

// 获取vpn状态 type-1关闭 2开启 空判断是否开启
export function getVpnFlag(type, currVpnFlag, toast) {
  return new Promise(resolve => {
    let encoding = "gbk";
    let binaryEncoding = "binary";
    cp.exec('REG QUERY "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v AutoConfigURL', {encoding: binaryEncoding}, function (err, stdout, stderr) {
      let data = getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding);
      let dataList = data.trim().replace(/\s/g, "~~").split("~~");
      trimArray(dataList);
      let vpnFlag = false;
      if (dataList[4]) {
        vpnFlag = true;
      }
      if (type) {
        toast({title: `切换${type == 1 ? "正常" : "专属"}线路${vpnFlag != currVpnFlag ? "成功" : "失败"}`});
      }
      resolve(vpnFlag);
    })
  });
}

// 判断文件是否heic
export function getIsHeic(base64) {
  let base64String = base64.replace(/^data:image\/.*?;base64,/, "");
  return /heic/.test(atob(base64String.slice(0, 38)))
}

// 将heic转成jpg
export async function heicToJpg(param) {
  let flag = false;
  let node = "";
  // 判断是否heic文件，是则转为jpg
  try {
    let base64 = param.base64;
    if (!base64 && param.localSrc) {
      base64 = await getLocalFile({filePath: param.localSrc, base64: true});
    }
    if (base64) {
      if (getIsHeic(base64)) {
        let pngBlob = await heic2any({
          blob: dataUrlToBlob(base64),
          toType: 'image/jpeg',
          quality: 1
        });
        if (pngBlob) {
          node = await fileToDataURL(pngBlob);
          await saveBase64Local({path: param.localSrc, base64: node});
        }
        flag = true;
      }
    }
  } catch (e) {}
  if (param.done) {
    return {flag, node};
  } else {
    return flag;
  }
}