/**
 * 2种截图工具SDK
 * */
import {getAppPath, getIconvDecode} from "@utils"
import {alert} from "@comp/ui";

let cp = remote.require("child_process");
let fs = remote.require("fs");
let readline = remote.require("readline");

let encoding = "gbk";
let binaryEncoding = "binary";

// snip是否启动了
let isOpenSnip = false;
let snipConfig = getAppPath(`\\Snipaste\\config.ini`);
let snipConfigBak = getAppPath(`\\Snipaste\\config.ini.bak`);
let snipLogPath = getAppPath(`\\Snipaste\\splog.txt`);
let snipPath = getAppPath(`\\Snipaste\\Snipaste.exe`);
let snipExecProcess = ""; // 执行截图功能进程
let snipExecProcessList = []; // 执行截图功能进程列表
let snipProcess = ""; //启动截图进程
let lastSnipTime = 0;// 最近一次打开截图时间

// win10用jt1 win10以下用jt2
let jt1Path = getAppPath(`\\Snipaste\\screenshot.exe`);
let jt2Path = getAppPath(`\\Snipaste\\screenshot_xp.exe`);


// 初始化截图
export function initJt(param) {
  if (!snipProcess) {
    loadSnip();
  }
  try {
    // 删除截图日志
    fs.access(snipLogPath, function (err) {
      if (!err) {
        fs.unlink(snipLogPath, function (err) { });
      }
    });
  } catch (e) {
  }
}

// 打开截图-1win10以下老截图-2win10以上老截图-3新截图
export function openJt(type) {
  return new Promise(resolve => {
    if (type == 1 || type == 2) {
      let thisJtPath = type == 1 ? jt1Path : jt2Path
      let jtProcess = cp.exec(`"${thisJtPath}"`, {encoding: binaryEncoding}, function (err, stdout, stderr) {
        console.log("jtProcess", getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding));
      });
      jtProcess.on("exit", function (code) {
        // 截图完成回调
        resolve();
      })
    } else if (type == 3) {
      watchJtLog(function () {
        // 截图完成回调
        resolve();
      });
    }
  });
}

// 启动snip进程
function loadSnip() {
  try {
    fs.copyFileSync(snipConfigBak, snipConfig);
  } catch (e) {}
  snipProcess = cp.exec(`"${snipPath}"`, function (err, stdout, stderr) {});
  // snip被退出自动重启
  snipProcess.on("exit", function (code) {
    if (code && code != 0) {
      loadSnip();
    }
  });
}

//监听snipaste日志变化判断是否截图完成
function watchJtLog(callback) {
  isOpenSnip = false;
  try {
    fs.watchFile(snipLogPath, {interval: 1}, function (event, filename) {
      let fRead = fs.createReadStream(snipLogPath);
      let objReadline = readline.createInterface({
        input: fRead
      });
      let lineStr = "";
      objReadline.on("line", function (line) {
        lineStr = line;
      });
      objReadline.on("close", function () {
        // 识别到截图则关闭打开截图子线程操作
        if (snipExecProcess) {
          snipExecProcess.kill();
          snipExecProcess = "";
        }
        isOpenSnip = true;
        console.log("splog:", lineStr);
        // 第一次启动了截图进程（子进程被杀的时候）
        if (/Initializing Snipaste/.test(lineStr)) {
          isOpenSnip = false;
          cpSnipExec(callback);
        }
        if (/Launch time/.test(lineStr)) {
          isOpenSnip = true;
        }
        if (isOpenSnip) {
          console.timeEnd("截图");
        }
        // 退出截图/截图保存成功后回调
        if (/Snipper: quit|Snipper: scene saved/.test(lineStr)) {
          unWatchJtLog();
          try {
            console.log("snipExecProcessList.length", snipExecProcessList.length);
            for (let i = 0; i < snipExecProcessList.length; i++) {
              snipExecProcessList[i].kill();
            }
            snipExecProcessList = [];
          } catch (e) {
            console.log("snipExecProcessListError", e);
          }
          callback();
        }
      });
    });
  } catch (e) {
    console.log("watchJtLogErr", e);
    unWatchJtLog();
    callback({err: true});
  }
  cpSnipExec(callback);
}

// 结束监听
function unWatchJtLog() {
  fs.unwatchFile(snipLogPath);
}

// 定时监控snip是否启动,init为初始化打开截图
function cpSnipExec(callback, flag) {
  if (!flag) {
    lastSnipTime = Date.now();
  }
  console.time("截图");
  console.log("snipStart", Date.now());
  snipExecProcess = cp.exec(`"${snipPath}" snip`, {encoding: binaryEncoding}, function (err, stdout, stderr) {
    console.log("snipEnd", Date.now(), getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding));
    // 识别到已经处于截图模式的时候不继续执行打开截图指令,截图指令有时候换不起截图
    // 有时候截图文件异常无法读取，超过5s不在继续执行
    if (!isOpenSnip && !err) {
      if (Date.now() - lastSnipTime >= 5 * 1000) {
        callback();
        alert({content: "截图工具异常，建议点击截图工具右边小三角，切换非内测版本截图工具", showCancel: false});
        // 重启截图工具
        cp.exec("taskkill /f /t /im Snipaste.exe", {encoding: binaryEncoding}, function (err, stdout, stderr) {});
        loadSnip()
      } else {
        cpSnipExec(callback, true);
      }
    }
  });
  snipExecProcessList.push(snipExecProcess);
}