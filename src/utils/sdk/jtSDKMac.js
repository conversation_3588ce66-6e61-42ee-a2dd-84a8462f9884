/**
 * Mac版本截图工具SDK
 * 使用Mac原生截图功能替代Snipaste
 */
import {getAppPath} from "@utils"
import {alert} from "@comp/ui";

let cp = remote.require("child_process");
let fs = remote.require("fs");
let path = remote.require("path");

// 平台检测
const isMac = process.platform === 'darwin';
const isWindows = process.platform === 'win32';

// 截图相关变量
let isOpenScreenshot = false;
let screenshotProcess = "";
let screenshotProcessList = [];
let lastScreenshotTime = 0;

// Mac截图临时文件路径
let screenshotTempPath = path.join(require('os').tmpdir(), 'leliao_screenshot.png');

// 初始化截图
export function initJt(param) {
  if (isMac) {
    // Mac版本初始化
    console.log("Mac screenshot initialized");
  } else {
    // Windows版本保持原样
    if (!snipProcess) {
      loadSnip();
    }
    try {
      // 删除截图日志
      fs.access(snipLogPath, function (err) {
        if (!err) {
          fs.unlink(snipLogPath, function (err) { });
        }
      });
    } catch (e) {
    }
  }
}

// 打开截图
// type: 1-区域截图, 2-全屏截图, 3-窗口截图
export function openJt(type = 1) {
  return new Promise(resolve => {
    if (isMac) {
      openMacScreenshot(type, resolve);
    } else {
      // Windows版本保持原样
      if (type == 1 || type == 2) {
        let thisJtPath = type == 1 ? jt1Path : jt2Path
        let jtProcess = cp.exec(`"${thisJtPath}"`, {encoding: binaryEncoding}, function (err, stdout, stderr) {
          console.log("jtProcess", getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding));
        });
        jtProcess.on("exit", function (code) {
          resolve();
        })
      } else if (type == 3) {
        watchJtLog(function () {
          resolve();
        });
      }
    }
  });
}

// Mac截图实现
function openMacScreenshot(type, callback) {
  console.time("Mac截图");
  console.log("macScreenshotStart", Date.now());
  
  let screenshotCmd;
  switch (type) {
    case 1:
      // 区域截图 - 交互式选择区域
      screenshotCmd = `screencapture -i "${screenshotTempPath}"`;
      break;
    case 2:
      // 全屏截图
      screenshotCmd = `screencapture "${screenshotTempPath}"`;
      break;
    case 3:
      // 窗口截图 - 交互式选择窗口
      screenshotCmd = `screencapture -w "${screenshotTempPath}"`;
      break;
    default:
      screenshotCmd = `screencapture -i "${screenshotTempPath}"`;
  }
  
  lastScreenshotTime = Date.now();
  screenshotProcess = cp.exec(screenshotCmd, function (err, stdout, stderr) {
    console.log("macScreenshotEnd", Date.now());
    console.timeEnd("Mac截图");
    
    if (err) {
      console.error("Screenshot error:", err);
      callback();
      return;
    }
    
    // 检查截图文件是否存在
    fs.access(screenshotTempPath, fs.constants.F_OK, (err) => {
      if (err) {
        console.log("Screenshot cancelled or failed");
        callback();
      } else {
        // 截图成功，处理文件
        handleScreenshotFile(callback);
      }
    });
  });
  
  screenshotProcessList.push(screenshotProcess);
}

// 处理截图文件
function handleScreenshotFile(callback) {
  try {
    // 读取截图文件
    fs.readFile(screenshotTempPath, (err, data) => {
      if (err) {
        console.error("Error reading screenshot:", err);
        callback();
        return;
      }
      
      // 将截图数据转换为base64
      let base64Data = data.toString('base64');
      let dataUrl = `data:image/png;base64,${base64Data}`;
      
      // 触发截图完成事件
      if (window.store) {
        window.store.commit("setEmit", {
          type: "screenshot",
          value: {
            dataUrl: dataUrl,
            filePath: screenshotTempPath,
            timestamp: Date.now()
          }
        });
      }
      
      // 清理临时文件
      setTimeout(() => {
        fs.unlink(screenshotTempPath, (err) => {
          if (err) console.log("Error cleaning up screenshot temp file:", err);
        });
      }, 5000);
      
      callback();
    });
  } catch (e) {
    console.error("Error handling screenshot file:", e);
    callback();
  }
}

// 获取截图快捷键设置
export function getJtKey() {
  if (isMac) {
    // Mac默认截图快捷键
    return {
      key1: "Cmd+Shift+4", // 区域截图
      key2: "Cmd+Shift+3", // 全屏截图
      key3: "Cmd+Shift+4+Space" // 窗口截图
    };
  } else {
    // Windows版本保持原样
    return {
      key1: "Alt+Shift+A",
      key2: "Alt+Ctrl+A"
    };
  }
}

// 注册截图快捷键
export function registerScreenshotShortcuts() {
  if (isMac) {
    // Mac版本使用系统快捷键，无需注册
    console.log("Mac screenshot shortcuts use system defaults");
    return true;
  } else {
    // Windows版本保持原样
    return registerWindowsShortcuts();
  }
}

// 检查截图工具是否可用
export function checkScreenshotAvailable() {
  if (isMac) {
    // Mac系统自带截图功能
    return Promise.resolve(true);
  } else {
    // Windows版本检查Snipaste
    return new Promise(resolve => {
      fs.access(snipPath, fs.constants.F_OK, (err) => {
        resolve(!err);
      });
    });
  }
}

// 获取截图工具版本信息
export function getScreenshotToolInfo() {
  if (isMac) {
    return {
      name: "macOS Screenshot",
      version: "System Built-in",
      platform: "macOS"
    };
  } else {
    return {
      name: "Snipaste",
      version: "Unknown",
      platform: "Windows"
    };
  }
}

// 停止所有截图进程
export function stopAllScreenshotProcesses() {
  if (isMac) {
    screenshotProcessList.forEach(process => {
      if (process && !process.killed) {
        process.kill('SIGTERM');
      }
    });
    screenshotProcessList = [];
  } else {
    // Windows版本保持原样
    snipExecProcessList.forEach(process => {
      if (process && !process.killed) {
        process.kill();
      }
    });
    snipExecProcessList = [];
  }
}

// 清理截图相关资源
export function cleanupScreenshot() {
  stopAllScreenshotProcesses();
  
  if (isMac) {
    // 清理Mac临时文件
    try {
      if (fs.existsSync(screenshotTempPath)) {
        fs.unlinkSync(screenshotTempPath);
      }
    } catch (e) {
      console.log("Error cleaning up screenshot temp file:", e);
    }
  }
}

// 导出默认对象
export default {
  initJt,
  openJt,
  getJtKey,
  registerScreenshotShortcuts,
  checkScreenshotAvailable,
  getScreenshotToolInfo,
  stopAllScreenshotProcesses,
  cleanupScreenshot
};
