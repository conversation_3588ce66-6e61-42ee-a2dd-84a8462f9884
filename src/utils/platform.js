/**
 * 平台检测和跨平台工具
 */
const os = require('os');
const path = require('path');

// 平台检测
export const platform = {
  isWindows: process.platform === 'win32',
  isMac: process.platform === 'darwin',
  isLinux: process.platform === 'linux',
  arch: process.arch,
  isARM: process.arch === 'arm64',
  isIntel: process.arch === 'x64'
};

// 路径分隔符
export const pathSep = platform.isWindows ? '\\' : '/';

// 获取平台特定的路径
export function getPlatformPath(...segments) {
  return path.join(...segments);
}

// 获取应用数据目录
export function getAppDataPath() {
  if (platform.isWindows) {
    return process.env.APPDATA;
  } else if (platform.isMac) {
    return path.join(os.homedir(), 'Library', 'Application Support');
  } else {
    return path.join(os.homedir(), '.config');
  }
}

// 获取临时目录
export function getTempPath() {
  return os.tmpdir();
}

// 获取用户主目录
export function getHomePath() {
  return os.homedir();
}

// 获取系统信息
export function getSystemInfo() {
  return {
    platform: process.platform,
    arch: process.arch,
    release: os.release(),
    type: os.type(),
    hostname: os.hostname(),
    cpus: os.cpus().length,
    totalmem: os.totalmem(),
    freemem: os.freemem()
  };
}

// 平台特定的命令
export const commands = {
  // 进程管理
  killProcess: platform.isWindows ? 'taskkill /f /pid' : 'kill -9',
  killProcessByName: platform.isWindows ? 'taskkill /f /im' : 'pkill -f',
  
  // 网络
  netstat: platform.isWindows ? 'netstat -ano' : 'netstat -an',
  findPort: platform.isWindows ? 'findstr' : 'grep',
  
  // 系统信息
  systemInfo: platform.isWindows ? 'systeminfo' : 'system_profiler',
  
  // 文件操作
  openFile: platform.isWindows ? 'start' : (platform.isMac ? 'open' : 'xdg-open'),
  openFolder: platform.isWindows ? 'explorer' : (platform.isMac ? 'open' : 'xdg-open')
};

// 平台特定的文件扩展名
export const extensions = {
  executable: platform.isWindows ? '.exe' : '',
  script: platform.isWindows ? '.bat' : '.sh',
  library: platform.isWindows ? '.dll' : (platform.isMac ? '.dylib' : '.so')
};

// 获取可执行文件路径
export function getExecutablePath(baseName) {
  return baseName + extensions.executable;
}

// 获取脚本文件路径
export function getScriptPath(baseName) {
  return baseName + extensions.script;
}

// 平台特定的环境变量
export function getEnvVar(name) {
  if (platform.isWindows) {
    return process.env[name];
  } else {
    return process.env[name];
  }
}

// 获取系统路径分隔符
export function getPathSeparator() {
  return path.sep;
}

// 标准化路径
export function normalizePath(inputPath) {
  return path.normalize(inputPath);
}

// 转换Windows路径为Unix路径
export function convertPath(windowsPath) {
  if (platform.isWindows) {
    return windowsPath;
  }
  return windowsPath.replace(/\\/g, '/');
}

// 获取平台特定的应用路径
export function getAppPath(relativePath = '', isPublic = false) {
  const basePath = process.cwd();
  const publicPath = isPublic ? path.join(basePath, 'public') : basePath;
  
  if (!relativePath) {
    return publicPath;
  }
  
  // 转换Windows风格的路径
  const normalizedPath = relativePath.replace(/\\/g, path.sep);
  return path.join(publicPath, normalizedPath);
}

export default {
  platform,
  pathSep,
  getPlatformPath,
  getAppDataPath,
  getTempPath,
  getHomePath,
  getSystemInfo,
  commands,
  extensions,
  getExecutablePath,
  getScriptPath,
  getEnvVar,
  getPathSeparator,
  normalizePath,
  convertPath,
  getAppPath
};
