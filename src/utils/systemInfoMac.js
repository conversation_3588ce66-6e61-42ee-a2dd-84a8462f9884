/**
 * Mac版本系统信息获取工具
 * 替代Windows的WMI调用
 */
const os = require('os');
const cp = require('child_process');
const fs = require('fs');
const path = require('path');

// 平台检测
const isMac = process.platform === 'darwin';
const isWindows = process.platform === 'win32';

// 获取系统信息
export function getSystemInfo() {
  return new Promise((resolve) => {
    if (isMac) {
      getMacSystemInfo().then(resolve);
    } else {
      getWindowsSystemInfo().then(resolve);
    }
  });
}

// 获取Mac系统信息
function getMacSystemInfo() {
  return new Promise((resolve) => {
    let systemInfo = {
      platform: 'darwin',
      arch: process.arch,
      release: os.release(),
      type: os.type(),
      hostname: os.hostname(),
      cpus: os.cpus().length,
      totalmem: os.totalmem(),
      freemem: os.freemem(),
      uptime: os.uptime()
    };

    // 获取详细的Mac系统信息
    let promises = [
      getMacHardwareInfo(),
      getMacSoftwareInfo(),
      getMacNetworkInfo()
    ];

    Promise.all(promises).then(([hardware, software, network]) => {
      systemInfo = {
        ...systemInfo,
        ...hardware,
        ...software,
        ...network
      };
      resolve(systemInfo);
    }).catch(() => {
      resolve(systemInfo);
    });
  });
}

// 获取Mac硬件信息
function getMacHardwareInfo() {
  return new Promise((resolve) => {
    cp.exec('system_profiler SPHardwareDataType -json', (err, stdout, stderr) => {
      if (err) {
        resolve({});
        return;
      }

      try {
        let data = JSON.parse(stdout);
        let hardware = data.SPHardwareDataType[0];
        
        resolve({
          model: hardware.machine_model || '',
          modelName: hardware.machine_name || '',
          processorName: hardware.cpu_type || '',
          processorSpeed: hardware.current_processor_speed || '',
          numberOfProcessors: hardware.number_processors || '',
          totalNumberOfCores: hardware.packages || '',
          memory: hardware.physical_memory || '',
          serialNumber: hardware.serial_number || '',
          hardwareUUID: hardware.platform_UUID || '',
          bootRomVersion: hardware.boot_rom_version || '',
          smcVersion: hardware.SMC_version_system || ''
        });
      } catch (e) {
        resolve({});
      }
    });
  });
}

// 获取Mac软件信息
function getMacSoftwareInfo() {
  return new Promise((resolve) => {
    cp.exec('system_profiler SPSoftwareDataType -json', (err, stdout, stderr) => {
      if (err) {
        resolve({});
        return;
      }

      try {
        let data = JSON.parse(stdout);
        let software = data.SPSoftwareDataType[0];
        
        resolve({
          systemVersion: software.os_version || '',
          kernelVersion: software.kernel_version || '',
          bootVolume: software.boot_volume || '',
          bootMode: software.boot_mode || '',
          computerName: software.local_host_name || '',
          userName: software.user_name || '',
          secureVirtualMemory: software.secure_virtual_memory || '',
          systemIntegrityProtection: software.system_integrity || ''
        });
      } catch (e) {
        resolve({});
      }
    });
  });
}

// 获取Mac网络信息
function getMacNetworkInfo() {
  return new Promise((resolve) => {
    let networkInfo = {
      interfaces: [],
      macAddresses: [],
      ipAddresses: []
    };

    try {
      let interfaces = os.networkInterfaces();
      for (let name in interfaces) {
        let iface = interfaces[name];
        for (let i = 0; i < iface.length; i++) {
          let alias = iface[i];
          if (alias.family === 'IPv4' && !alias.internal) {
            networkInfo.interfaces.push({
              name: name,
              address: alias.address,
              netmask: alias.netmask,
              mac: alias.mac
            });
            networkInfo.ipAddresses.push(alias.address);
            if (alias.mac && alias.mac !== '00:00:00:00:00:00') {
              networkInfo.macAddresses.push(alias.mac);
            }
          }
        }
      }
    } catch (e) {
      console.log('Error getting network info:', e);
    }

    resolve(networkInfo);
  });
}

// 获取Windows系统信息（保持原有逻辑）
function getWindowsSystemInfo() {
  return new Promise((resolve) => {
    // 这里保持原有的Windows系统信息获取逻辑
    resolve({
      platform: 'win32',
      // ... 其他Windows特定信息
    });
  });
}

// 获取磁盘信息
export function getDiskInfo() {
  return new Promise((resolve) => {
    if (isMac) {
      getMacDiskInfo().then(resolve);
    } else {
      getWindowsDiskInfo().then(resolve);
    }
  });
}

// 获取Mac磁盘信息
function getMacDiskInfo() {
  return new Promise((resolve) => {
    cp.exec('system_profiler SPStorageDataType -json', (err, stdout, stderr) => {
      if (err) {
        resolve([]);
        return;
      }

      try {
        let data = JSON.parse(stdout);
        let storage = data.SPStorageDataType || [];
        
        let diskInfo = storage.map(disk => ({
          name: disk._name || '',
          size: disk.size_in_bytes || '',
          available: disk.free_space_in_bytes || '',
          used: disk.used_space_in_bytes || '',
          mountPoint: disk.mount_point || '',
          fileSystem: disk.file_system || '',
          volumeUUID: disk.volume_uuid || ''
        }));
        
        resolve(diskInfo);
      } catch (e) {
        resolve([]);
      }
    });
  });
}

// 获取Windows磁盘信息（保持原有逻辑）
function getWindowsDiskInfo() {
  return new Promise((resolve) => {
    // 保持原有的Windows磁盘信息获取逻辑
    resolve([]);
  });
}

// 获取CPU使用率
export function getCpuUsage() {
  return new Promise((resolve) => {
    if (isMac) {
      getMacCpuUsage().then(resolve);
    } else {
      getWindowsCpuUsage().then(resolve);
    }
  });
}

// 获取Mac CPU使用率
function getMacCpuUsage() {
  return new Promise((resolve) => {
    cp.exec('top -l 1 -n 0 | grep "CPU usage"', (err, stdout, stderr) => {
      if (err) {
        resolve({ usage: 0 });
        return;
      }

      try {
        // 解析top命令输出
        let match = stdout.match(/(\d+\.\d+)% user/);
        let usage = match ? parseFloat(match[1]) : 0;
        resolve({ usage: usage });
      } catch (e) {
        resolve({ usage: 0 });
      }
    });
  });
}

// 获取Windows CPU使用率（保持原有逻辑）
function getWindowsCpuUsage() {
  return new Promise((resolve) => {
    // 保持原有的Windows CPU使用率获取逻辑
    resolve({ usage: 0 });
  });
}

// 获取内存使用情况
export function getMemoryUsage() {
  return new Promise((resolve) => {
    if (isMac) {
      getMacMemoryUsage().then(resolve);
    } else {
      getWindowsMemoryUsage().then(resolve);
    }
  });
}

// 获取Mac内存使用情况
function getMacMemoryUsage() {
  return new Promise((resolve) => {
    cp.exec('vm_stat', (err, stdout, stderr) => {
      if (err) {
        resolve({
          total: os.totalmem(),
          free: os.freemem(),
          used: os.totalmem() - os.freemem()
        });
        return;
      }

      try {
        let lines = stdout.split('\n');
        let pageSize = 4096; // Mac页面大小通常是4KB
        let stats = {};
        
        lines.forEach(line => {
          if (line.includes('Pages free:')) {
            stats.free = parseInt(line.match(/\d+/)[0]) * pageSize;
          } else if (line.includes('Pages active:')) {
            stats.active = parseInt(line.match(/\d+/)[0]) * pageSize;
          } else if (line.includes('Pages inactive:')) {
            stats.inactive = parseInt(line.match(/\d+/)[0]) * pageSize;
          } else if (line.includes('Pages wired down:')) {
            stats.wired = parseInt(line.match(/\d+/)[0]) * pageSize;
          }
        });
        
        resolve({
          total: os.totalmem(),
          free: stats.free || os.freemem(),
          used: (stats.active || 0) + (stats.wired || 0),
          active: stats.active || 0,
          inactive: stats.inactive || 0,
          wired: stats.wired || 0
        });
      } catch (e) {
        resolve({
          total: os.totalmem(),
          free: os.freemem(),
          used: os.totalmem() - os.freemem()
        });
      }
    });
  });
}

// 获取Windows内存使用情况（保持原有逻辑）
function getWindowsMemoryUsage() {
  return new Promise((resolve) => {
    // 保持原有的Windows内存使用情况获取逻辑
    resolve({
      total: os.totalmem(),
      free: os.freemem(),
      used: os.totalmem() - os.freemem()
    });
  });
}

// 导出默认对象
export default {
  getSystemInfo,
  getDiskInfo,
  getCpuUsage,
  getMemoryUsage
};
