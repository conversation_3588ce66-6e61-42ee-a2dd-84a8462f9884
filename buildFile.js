const archiver = require("archiver");
const glob = require("glob");
const path = require("path");
const fs = require("fs");
const childProcess = require("child_process");
const terser = require("terser");
const cp = require("child_process");
const iconv = require("iconv-lite");

let env = getArg("env");
let version = getArg("version");

let encoding = "gbk";
let binaryEncoding = "binary";

let p1 = nwjc("index");
let p2 = nwjc("forward");
let p3 = nwjc("notify");
let p4 = nwjc("viewer");
let p5 = nwjc("shortcutConflict");
let p6 = nwjc("netDetect");
let p7 = nwjc("js/jsqr");
let p8 = nwjc("js/public");
let p9 = nwjc("js/captcha");

let p101 = nwjc("chunk-index");
let p102 = nwjc("chunk-child");
let p103 = nwjc("chunk-common");
let p104 = nwjc("chunk-iconv");
let p105 = nwjc("chunk-dexie");

Promise.all([p1, p2, p3, p4, p5, p6, p7, p8, p9, p101, p102, p103, p104, p105]).then(function () {
  // 删除截图历史
  unlinkDir(path.join(__dirname, "/Snipaste/history"));
  unlinkDir(path.join(__dirname, "/Snipaste/crashes"));
  unlinkDir(path.join(__dirname, "/dist/nw"));
  // 生成文件夹
  mkdirsSync(path.join(__dirname, "/dist/win-ia32-unpacked/Snipaste/"));
  mkdirsSync(path.join(__dirname, "/dist/win-ia32-unpacked/node_modules/adm-zip/"));
  // 新增代理request依赖
  setRequestFile();
  // 复制到压缩包目录
  copyFile(path.join(__dirname, "/package.json"), path.join(__dirname, "/dist/win-ia32-unpacked/package.json"));
  let p = [
    uglifyJs(path.join(__dirname, "/config.js"), path.join(__dirname, "/dist/win-ia32-unpacked/config.js"), false),
    uglifyJs(path.join(__dirname, "/background.js"), path.join(__dirname, "/dist/win-ia32-unpacked/background.js"), true),
  ];
  Promise.allSettled(p).then(res => {
    // 生成文件夹
    mkdirsSync(path.join(__dirname, "/dist/nw/"));
    mkdirsSync(path.join(__dirname, "/dist/nw/Snipaste/"));
    mkdirsSync(path.join(__dirname, "/dist/nw/node_modules/adm-zip/"));
    // 复制到打包目录
    copyAllFile(path.join(__dirname, "/node_modules/adm-zip"), path.join(__dirname, "/dist/nw/node_modules/adm-zip"));
    copyAllFile(path.join(__dirname, "/Snipaste"), path.join(__dirname, "/dist/nw/Snipaste"));
    copyAllFile(path.join(__dirname, "/dist/win-ia32-unpacked"), path.join(__dirname, "/dist/nw"));
    // 复制nw程序到目录
    let fileReg = /\.exe|\.nexe|\.dll|\.bat|\.bin|(\.json&!temp.json)|\.pak|\.dat|\.png|\.pem|\.key/;
    let dirReg = /locales|pnacl|swiftshader/;
    copyAllFile(path.join(__dirname), path.join(__dirname, "/dist/nw"), fileReg, dirReg);

    // 打包压缩包
    createZip();

    // 生成安装包
    install();
  });
});


// 复制文件/目录
function copyAllFile(src, dist, fileReg, dirReg) {
  let files, stat, _src, _dist;
  files = fs.readdirSync(src);
  files.forEach(function (file) {
    _src = src + path.sep + file;
    _dist = dist + path.sep + file;
    stat = fs.statSync(_src);
    if (stat.isFile()) {
      if (/安装向导\.exe/.test(file)) {
        fs.unlinkSync(_src);
      } else if (!fileReg || fileReg.test(file)) {
        console.log(_src, "==========>", _dist);
        copyFile(_src, _dist);
      }
    } else {
      if (!dirReg || dirReg.test(file)) {
        mkdirsSync(_dist);
        copyAllFile(_src, _dist, fileReg, dirReg);
      }
    }
  });
}

function copyFile(_src, _dist) {
  try {
    fs.writeFileSync(_dist, fs.readFileSync(_src), "utf8");
  } catch (error) {
    console.error("copyFile", error);
    fs.unlinkSync(_dist);
    fs.writeFileSync(_dist, fs.readFileSync(_src), "utf8");
  }
}

// 压缩混淆js文件
async function uglifyJs(fileIn, fileOut, mangle) {
  let readCode = fs.readFileSync(fileIn, "utf8");
  let res = await terser.minify(readCode, {mangle: mangle});
  fs.writeFileSync(fileOut, res.code, "utf8");
}

// 递归创建目录 同步方法
function mkdirsSync(dirname) {
  if (fs.existsSync(dirname)) {
    return true;
  } else {
    if (mkdirsSync(path.dirname(dirname))) {
      fs.mkdirSync(dirname);
      return true;
    }
  }
}

// 删除目录
function unlinkDir(dir) {
  try {
    let files = fs.readdirSync(dir);
    files.forEach(function (file) {
      let stats = fs.statSync(`${dir}/${file}`);
      if (stats.isDirectory()) {
        unlinkDir(`${dir}/${file}`);
      } else {
        fs.unlinkSync(`${dir}/${file}`);
      }
    });
    fs.rmdirSync(dir);
  } catch (error) {
    console.error("unlinkDir", error);
  }
}

// 打包zip
function createZip() {
  let dirPath = path.join(__dirname, "/dist/win-ia32-unpacked");
  let targetPath = path.join(__dirname, "/dist/");
  let targetName = "win-ia32-unpacked.zip"
  let output = fs.createWriteStream(targetPath + targetName);
  let archive = archiver("zip", {
    store: true
  });

  output.on("close", function () {

  });

  archive.on("error", function (err) {
    throw err;
  });

  let files = glob.sync("**/*.*", {
    cwd: dirPath,
    dot: false
  });

  files.forEach(function (file) {
    if (file != "app.asar.unpacked") {
      let filePath = path.resolve(dirPath, file);
      console.log("[archive] " + filePath);
      archive.append(fs.createReadStream(filePath), {
        "name": file,
        "mode": fs.statSync(filePath).mode
      });
    }
  });

  archive.pipe(output);
  archive.finalize();
}

// 生成exe
function install() {
  let theNsi = path.join(__dirname, "JJS_IM.nsi");
  let theDir = path.join(__dirname, "/dist/nw");
  let theEnv = (env == "online" ? "" : `-${env}`);
  let thePath = "D:\\leyoujiaIm" + theEnv;
  let name = "乐聊" + theEnv;
  let eName = `jjsim${env == "online" ? "" : env}`;
  let command = `makensis /DPRODUCT_URL=${eName} /DPRODUCT_NAME=${name} /DPRODUCT_VERSION=${version} /DPRODUCT_PATH=${thePath} /DPRODUCT_DIR=${theDir} ${theNsi}`;
  console.log("exec command:" + command);
  return new Promise(function (resolve, reject) {
    let cp = childProcess.exec(command, {
      encoding: "binary"
    }, function (error, stdout, stderr) {
      console.log("[nsis make done!]");
    }).on("close", function (code) {
      resolve(code);
    }).on("error", function (error) {
      reject(error);
    });
    cp.stdout.on("data", function (data) {
      console.log(data);
    });
  });
}

// 获取参数
function getArg(key) {
  let argv = process.argv;
  let index = argv.findIndex(item => new RegExp(key).test(item))
  if (index != -1) {
    return argv[index].split("=")[1]
  }
  return ""
}

// 新增代理request依赖
function setRequestFile() {
  let fileList = [
    ".bin", "ajv", "asn1", "assert-plus", "asynckit", "aws4", "aws-sign2", "bcrypt-pbkdf", "caseless", "combined-stream", "core-util-is", "dashdash", "delayed-stream",
    "ecc-jsbn", "extend", "extsprintf", "fast-deep-equal", "fast-json-stable-stringify", "forever-agent", "form-data", "getpass", "har-schema", "har-validator",
    "http-signature", "isstream", "is-typedarray", "jsbn", "json-schema", "json-schema-traverse", "json-stringify-safe", "jsprim", "mime-db", "mime-types", "oauth-sign",
    "performance-now", "psl", "punycode", "qs", "request", "safe-buffer", "safer-buffer", "sshpk", "tough-cookie", "tunnel-agent", "tweetnacl", "uri-js", "uuid", "verror",
    "ws", "async-limiter", "iconv-lite", "heic2any",
  ];
  fileList.map(item => {
    mkdirsSync(path.join(__dirname, `/dist/win-ia32-unpacked/node_modules/${item}/`));
    copyAllFile(path.join(__dirname, `/node_modules/${item}`), path.join(__dirname, `/dist/win-ia32-unpacked/node_modules/${item}`));
    mkdirsSync(path.join(__dirname, `/dist/nw/node_modules/${item}/`));
    copyAllFile(path.join(__dirname, `/node_modules/${item}`), path.join(__dirname, `/dist/nw/node_modules/${item}`));
  });
}

// nwjc加密文件
function nwjc(fileName) {
  return new Promise(function (resolve) {
    let fileDir = `${__dirname}/dist/win-ia32-unpacked/`;
    let filePath = fileDir + fileName + ".js";
    cp.exec(`nwjc ${filePath} ${fileDir}${fileName}.bin`, {encoding: binaryEncoding}, function (err, stdout, stderr) {
      if (stderr) {
        console.log("nwjcErr", iconv.decode(new Buffer(stderr, binaryEncoding), encoding));
        return;
      }
      fs.unlinkSync(filePath);
      resolve();
    });
  });
}