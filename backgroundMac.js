// Mac版本的background.js - 判断是否存在更新包，启动更新
let iconv = require("iconv-lite");
let https = require("https");
let WebSocket = require("ws");
let fs = require("fs");
let path = require("path");
let cp = require("child_process");
let AdmZip = require("adm-zip");
let vipInfoPath = path.join(process.cwd(), "/temp.json");
let packageInfo = require("./package.json");
let isFullscreen = false;
let wssCount = 0;
let reAutoInstallFlag = false;

// 平台检测
const isMac = process.platform === 'darwin';
const isWindows = process.platform === 'win32';

if (process.env.NODE_ENV != "development") {
  // 删除历史js文件
  unlinkSync(path.join(process.cwd(), "/chunk-index.js"));
  unlinkSync(path.join(process.cwd(), "/chunk-child.js"));
  unlinkSync(path.join(process.cwd(), "/chunk-common.js"));
  unlinkSync(path.join(process.cwd(), "/chunk-dexie.js"));
  unlinkSync(path.join(process.cwd(), "/chunk-iconv.js"));
  unlinkSync(path.join(process.cwd(), "/index.js"));
  unlinkSync(path.join(process.cwd(), "/forward.js"));
  unlinkSync(path.join(process.cwd(), "/netDetect.js"));
  unlinkSync(path.join(process.cwd(), "/notify.js"));
  unlinkSync(path.join(process.cwd(), "/shortcutConflict.js"));
  unlinkSync(path.join(process.cwd(), "/viewer.js"));
  unlinkSync(path.join(process.cwd(), "/js/captcha.js"));
  unlinkSync(path.join(process.cwd(), "/js/jsqr.js"));
  unlinkSync(path.join(process.cwd(), "/js/public.js"));
}

// 判断版本号一致删除更新文件
if (fs.existsSync(vipInfoPath)) {
  try {
    // 异常则不更新
    let tempJson = JSON.parse(fs.readFileSync(vipInfoPath));
    if (packageInfo.currentVersion == tempJson.fileVersion || !fs.existsSync(tempJson.fileUrl)) {
      unlinkSync(vipInfoPath);
      unlinkSync(tempJson.fileUrl);
    }
  } catch (e) {
    unlinkSync(vipInfoPath);
    try {
      reluanchApp();
    } catch (e) {}
  }
}

if (fs.existsSync(vipInfoPath) && process.env.NODE_ENV != "development") {
  try {
    // 异常则不更新
    let tempJson = JSON.parse(fs.readFileSync(vipInfoPath));
    if (tempJson.fileExt == ".zip") {
      let zip = new AdmZip(tempJson.fileUrl);
      zip.extractAllTo(process.cwd(), true);
      reluanchApp();
    } else {
      autoInstall(tempJson.fileUrl);
    }
  } catch (e) {
    try {
      fs.unlinkSync(vipInfoPath);
      reluanchApp();
    } catch (e) {}
  }
} else {
  // 设置最小字体9px
  chrome.fontSettings.setMinimumFontSize({pixelSize: 9});
  global.childWin = {};
  global.childWinLoadingMap = {};
  global.registerMap = {};
  global.trayObj = {};
  global.mainNW = nw;
  
  // 托盘闪烁
  global.trayObj.trayInterval = setInterval(() => {
    if (global.trayObj.trayFlagInterval) {
      if (global.trayObj.trayFlag) {
        if (global.trayObj.netStatus == 1) {
          global.trayObj.tray.icon = "/tray.png";
        } else {
          global.trayObj.tray.icon = "/tray_gray.png";
        }
      } else {
        global.trayObj.tray.icon = "/tray_flash.png";
      }
      global.trayObj.trayFlag = !global.trayObj.trayFlag;
    }
  }, 600);
  
  nw.Window.open(process.env.NODE_ENV != "development" ? `/index.html#/` : `http://localhost:8888#/`, {
    frame: false,
    transparent: true,
    show: false,
    position: "center",
  }, (win) => {
    global.mainWin = win;
    
    // 打开应用
    nw.App.on("open", function (a, b) {
      win.window.console.log("openIM", a, b)
      win.window.store.commit("setWindowCancelMin", "main");
    });

    win.on("document-start", () => {
      global.mainWin = win;
      // 开发环境热加载关闭所有窗口
      if (process.env.NODE_ENV == "development") {
        closeAllWin(win);
      }
    });
    
    win.on("document-end", () => {
      win.show(true);
    });

    // 聚焦窗口
    win.on("focus", () => {
      win.window.nw.Window.get().isFocused = true;
      win.window.store.commit("setEmit", {type: "focus", value: Date.now()});
    });
    
    // 失焦窗口
    win.on("blur", () => {
      win.window.nw.Window.get().isFocused = false;
      win.window.store.commit("setEmit", {type: "blur", value: Date.now()});
    });

    // 关闭窗口
    win.on("close", () => {
      win.hide();
      killSnip();
    });
    
    // 关闭窗口
    win.on("closed", () => {
      win.hide();
      killSnip();
    });
    
    // 关闭窗口
    win.on("quit", () => {
      // 移除托盘图标
      trayObj.tray.remove();
      killSnip();
      trayObj.tray = null;
    });
    
    // 调整窗口大小
    win.on("resize", () => {
      emitResize(win, true);
    });
    
    win.on("enter-fullscreen", () => {
      isFullscreen = true;
      emitResize(win);
    });
    
    win.on("restore", () => {
      if (isFullscreen) {
        isFullscreen = false;
        emitResize(win, true);
      }
    });
    
    win.on('navigation', function (frame, url, policy) {
      policy.ignore();
      // 在系统默认浏览器打开
      nw.Shell.openExternal(url);
    });
    
    win.on('new-win-policy', function (frame, url, policy) {
      // 不打开窗口
      policy.ignore();
      // 在系统默认浏览器打开
      win.window.nw.utils.setOpenExternal(url);
    });

    initWss(win);
    setHosts(win);
  });
}

// 结束截图进程 - Mac版本
function killSnip() {
  if (isMac) {
    // Mac上使用pkill结束截图进程
    cp.exec("pkill -f 'screenshot'", function (err, stdout, stderr) {});
  } else {
    // Windows版本保持原样
    cp.exec("taskkill /f /t /im Snipaste.exe", {encoding: "binary"}, function (err, stdout, stderr) {});
  }
}

// 触发窗口大小变化方法
function emitResize(mainWin, resizeFlag) {
  mainWin.window.nw.utils.debounce({
    timerName: "emitResize",
    time: 500,
    fnName: function () {
      // 一分钟记录一次
      if (Date.now() - (global.ResizeLogTime || 0) > 60000) {
        mainWin.window.console.log("screens", mainWin.window.nw.Screen.screens);
        mainWin.window.console.log("winBounds", mainWin.getBounds());
        global.ResizeLogTime = Date.now();
      }
      mainWin.window.store.commit("setEmit", {type: "resize", value: {win: mainWin.cWindow.id, resizeFlag: resizeFlag, time: Date.now()}});
      mainWin.window.store.commit("setEmit", {type: "isFullscreen", value: mainWin.isFullscreen});

      if (mainWin.maxWidth != mainWin.window.screen.availWidth || mainWin.maxHeight != mainWin.window.screen.availHeight) {
        mainWin.maxWidth = mainWin.window.screen.availWidth;
        mainWin.maxHeight = mainWin.window.screen.availHeight;
        mainWin.window.console.log("setWinMaxSize", {maxW: mainWin.window.screen.availWidth, maxH: mainWin.window.screen.availHeight});
        mainWin.setMaximumSize(mainWin.window.screen.availWidth, mainWin.window.screen.availHeight);
      }
    }
  });
}

// 关闭所有窗口
function closeAllWin(mainWin) {
  nw.Window.getAll(allWin => {
    for (let i = 0; i < allWin.length; i++) {
      if (allWin[i].cWindow.id != 1 && allWin[i].cWindow.id != mainWin.cWindow.id) {
        allWin[i].close(true);
      }
    }
  });
  for (let key in global.childWin) {
    delete global.childWin[key];
  }
}

// 重启应用 - Mac版本
function reluanchApp() {
  try {
    global.trayObj.tray.remove();
  } catch (e) {}
  try {
    if (global.cmdRelunach) {
      global.cmdRelunach = false;
      if (isMac) {
        // Mac版本：创建重启应用脚本
        let restartScript = path.join(process.cwd(), "/restart.sh");
        fs.writeFileSync(restartScript, `#!/bin/bash\nkill -9 ${process.ppid}\nopen "${process.execPath}"\nexit`, "utf-8");
        fs.chmodSync(restartScript, 0o755);
        nw.Shell.openItem(restartScript);
      } else {
        // Windows版本保持原样
        fs.writeFileSync(path.join(process.cwd(), "/restart.bat"), `@echo off \n taskkill /f /pid ${process.ppid} \n explorer.exe ${process.execPath} \n exit`, "utf-8");
        nw.Shell.openItem(path.join(process.cwd(), "/restart.bat"));
      }
    } else {
      reluanch();
    }
  } catch (e) {
    reluanch();
  }
}

global.reluanchApp = reluanchApp;
global.autoInstall = autoInstall;

function reluanch() {
  let child = cp.spawn(process.execPath, [], {detached: true});
  child.unref();
  nw.App.quit();
}
