// 判断是否存在更新包，启动更新
let iconv = require("iconv-lite");
let https = require("https");
let WebSocket = require("ws");
let fs = require("fs");
let path = require("path");
let cp = require("child_process");
let AdmZip = require("adm-zip");
let vipInfoPath = path.join(process.cwd(), "/temp.json");
let packageInfo = require("./package.json");
let isFullscreen = false;
let wssCount = 0;
let reAutoInstallFlag = false;
if (process.env.NODE_ENV != "development") {
  // 删除历史js文件
  unlinkSync(path.join(process.cwd(), "/chunk-index.js"));
  unlinkSync(path.join(process.cwd(), "/chunk-child.js"));
  unlinkSync(path.join(process.cwd(), "/chunk-common.js"));
  unlinkSync(path.join(process.cwd(), "/chunk-dexie.js"));
  unlinkSync(path.join(process.cwd(), "/chunk-iconv.js"));
  unlinkSync(path.join(process.cwd(), "/index.js"));
  unlinkSync(path.join(process.cwd(), "/forward.js"));
  unlinkSync(path.join(process.cwd(), "/netDetect.js"));
  unlinkSync(path.join(process.cwd(), "/notify.js"));
  unlinkSync(path.join(process.cwd(), "/shortcutConflict.js"));
  unlinkSync(path.join(process.cwd(), "/viewer.js"));
  unlinkSync(path.join(process.cwd(), "/js/captcha.js"));
  unlinkSync(path.join(process.cwd(), "/js/jsqr.js"));
  unlinkSync(path.join(process.cwd(), "/js/public.js"));
}
// 判断版本号一致删除更新文件
if (fs.existsSync(vipInfoPath)) {
  try {
    // 异常则不更新
    let tempJson = JSON.parse(fs.readFileSync(vipInfoPath));
    if (packageInfo.currentVersion == tempJson.fileVersion || !fs.existsSync(tempJson.fileUrl)) {
      // 解决卸载.exe乱码问题，需要多解压一次
      let uninstallErrorPath = path.join(process.cwd(), "ж��.exe");
      if (fs.existsSync(uninstallErrorPath)) {
        unlinkSync(path.join(process.cwd(), "ж��.exe"));
      } else {
        unlinkSync(vipInfoPath);
        unlinkSync(tempJson.fileUrl);
      }
    }
  } catch (e) {
    unlinkSync(vipInfoPath);
    try {
      reluanchApp();
    } catch (e) {}
  }
}
if (fs.existsSync(vipInfoPath) && process.env.NODE_ENV != "development") {
  try {
    // 异常则不更新
    let tempJson = JSON.parse(fs.readFileSync(vipInfoPath));
    if (tempJson.fileExt == ".zip") {
      let zip = new AdmZip(tempJson.fileUrl);
      zip.extractAllTo(process.cwd(), true);
      reluanchApp();
    } else {
      autoInstall(tempJson.fileUrl);
    }
  } catch (e) {
    try {
      fs.unlinkSync(vipInfoPath);
      reluanchApp();
    } catch (e) {}
  }
} else {
  // 设置最小字体9px
  chrome.fontSettings.setMinimumFontSize({pixelSize: 9});
  global.childWin = {};
  global.childWinLoadingMap = {};
  global.registerMap = {};
  global.trayObj = {};
  global.mainNW = nw;
  // 托盘闪烁
  global.trayObj.trayInterval = setInterval(() => {
    if (global.trayObj.trayFlagInterval) {
      if (global.trayObj.trayFlag) {
        if (global.trayObj.netStatus == 1) {
          global.trayObj.tray.icon = "/tray.png";
        } else {
          global.trayObj.tray.icon = "/tray_gray.png";
        }
      } else {
        global.trayObj.tray.icon = "/tray_flash.png";
      }
      global.trayObj.trayFlag = !global.trayObj.trayFlag;
    }
  }, 600);
  nw.Window.open(process.env.NODE_ENV != "development" ? `/index.html#/` : `http://localhost:8888#/`, {
    frame: false,
    transparent: true,
    show: false,
    position: "center",
  }, (win) => {
    global.mainWin = win;
    // 打开应用
    nw.App.on("open", function (a, b) {
      win.window.console.log("openIM", a, b)
      win.window.store.commit("setWindowCancelMin", "main");
    });

    win.on("document-start", () => {
      global.mainWin = win;
      // 开发环境热加载关闭所有窗口
      if (process.env.NODE_ENV == "development") {
        closeAllWin(win);
      }
    });
    win.on("document-end", () => {
      win.show(true);
    });

    // 聚焦窗口
    win.on("focus", () => {
      win.window.nw.Window.get().isFocused = true;
      win.window.store.commit("setEmit", {type: "focus", value: Date.now()});
    });
    // 失焦窗口
    win.on("blur", () => {
      win.window.nw.Window.get().isFocused = false;
      win.window.store.commit("setEmit", {type: "blur", value: Date.now()});
    });

    // 关闭窗口
    win.on("close", () => {
      win.hide();
      killSnip();
    });
    // 关闭窗口
    win.on("closed", () => {
      win.hide();
      killSnip();
    });
    // 关闭窗口
    win.on("quit", () => {
      // 移除托盘图标
      trayObj.tray.remove();
      killSnip();
      trayObj.tray = null;
    });
    // 调整窗口大小
    win.on("resize", () => {
      emitResize(win, true);
    });
    win.on("enter-fullscreen", () => {
      isFullscreen = true;
      emitResize(win);
    });
    win.on("restore", () => {
      if (isFullscreen) {
        isFullscreen = false;
        emitResize(win, true);
      }
    });
    win.on('navigation', function (frame, url, policy) {
      policy.ignore();
      // 在系统默认浏览器打开
      nw.Shell.openExternal(url);
    });
    win.on('new-win-policy', function (frame, url, policy) {
      // 不打开窗口
      policy.ignore();
      // 在系统默认浏览器打开
      win.window.nw.utils.setOpenExternal(url);
    });

    initWss(win);
    setHosts(win);
  });
}

// 结束截图进程
function killSnip() {
  require("child_process").exec("taskkill /f /t /im Snipaste.exe", {encoding: "binary"}, function (err, stdout, stderr) {});
}

// 触发窗口大小变化方法
function emitResize(mainWin, resizeFlag) {
  mainWin.window.nw.utils.debounce({
    timerName: "emitResize",
    time: 500,
    fnName: function () {
      // 一分钟记录一次
      if (Date.now() - (global.ResizeLogTime || 0) > 60000) {
        mainWin.window.console.log("screens", mainWin.window.nw.Screen.screens);
        mainWin.window.console.log("winBounds", mainWin.getBounds());
        global.ResizeLogTime = Date.now();
      }
      mainWin.window.store.commit("setEmit", {type: "resize", value: {win: mainWin.cWindow.id, resizeFlag: resizeFlag, time: Date.now()}});
      mainWin.window.store.commit("setEmit", {type: "isFullscreen", value: mainWin.isFullscreen});

      if (mainWin.maxWidth != mainWin.window.screen.availWidth || mainWin.maxHeight != mainWin.window.screen.availHeight) {
        mainWin.maxWidth = mainWin.window.screen.availWidth;
        mainWin.maxHeight = mainWin.window.screen.availHeight;
        mainWin.window.console.log("setWinMaxSize", {maxW: mainWin.window.screen.availWidth, maxH: mainWin.window.screen.availHeight});
        mainWin.setMaximumSize(mainWin.window.screen.availWidth, mainWin.window.screen.availHeight);
      }
    }
  });
}

// 关闭所有窗口
function closeAllWin(mainWin) {
  nw.Window.getAll(allWin => {
    for (let i = 0; i < allWin.length; i++) {
      if (allWin[i].cWindow.id != 1 && allWin[i].cWindow.id != mainWin.cWindow.id) {
        allWin[i].close(true);
      }
    }
  });
  for (let key in global.childWin) {
    delete global.childWin[key];
  }
}

// 重启应用
function reluanchApp() {
  try {
    global.trayObj.tray.remove();
  } catch (e) {}
  try {
    if (global.cmdRelunach) {
      global.cmdRelunach = false;
      // 创建重启应用bat
      fs.writeFileSync(path.join(process.cwd(), "/restart.bat"), `@echo off \n taskkill /f /pid ${process.ppid} \n explorer.exe ${process.execPath} \n exit`, "utf-8");
      nw.Shell.openItem(path.join(process.cwd(), "/restart.bat"));
    } else {
      reluanch();
    }
  } catch (e) {
    reluanch();
  }
}

global.reluanchApp = reluanchApp;
global.autoInstall = autoInstall;

function reluanch() {
  let child = cp.spawn(process.execPath, [], {detached: true});
  child.unref();
  nw.App.quit();
}

// 自动安装更新包
function autoInstall(exePath) {
  let updateBat = path.join(process.cwd(), "/update.bat");
  if (fs.existsSync(exePath)) {
    // 存在中文空格目录安装失败,重新尝试安装
    reAutoInstall(exePath, updateBat);
  } else {
    fs.writeFileSync(updateBat, `@echo off \n taskkill /f /im CloudIM.exe \n taskkill /f /im snipaste.exe \n if "%1" == "h" goto begin \n mshta vbscript:createobject("wscript.shell").run("%~nx0 h",0)(window.close)&&exit \n :begin \n ${exePath} /skip \\D=${process.cwd()} \n exit`, "utf-8");
    // 存在中文空格目录安装失败,重新尝试安装
    setTimeout(() => {
      reAutoInstall(exePath, updateBat);
    }, 1000);
  }
  nw.Shell.openItem(updateBat);
}

// 重新尝试安装
function reAutoInstall(exePath, updateBat) {
  if (reAutoInstallFlag) {
    return;
  }
  reAutoInstallFlag = true;
  let autoBat = path.join(process.env.APPDATA, "autoIm.txt")
  // 中文路径无法安装
  fs.writeFileSync(autoBat, `"${exePath}" /skip \\D="${process.cwd()}"`, "utf-8");
  fs.writeFileSync(updateBat, `@echo off \n chcp 65001 \n taskkill /f /im CloudIM.exe \n taskkill /f /im snipaste.exe \n if "%1" == "h" goto begin \n mshta vbscript:createobject("wscript.shell").run("%~nx0 h",0)(window.close)&&exit \n :begin \n for /f "delims=" %%a in (${autoBat}) do (start "" %%a) \n exit`, "utf-8");
}

// 初始化wss
function initWss(win) {
  try {
    // 开启服务器
    let port = packageInfo.env == "online" ? "29286" : "29287";
    const privateKey = fs.readFileSync(path.join(process.cwd(), "/localhost_c.key"), "utf-8");
    const certificate = fs.readFileSync(path.join(process.cwd(), "/localhost_c.pem"), "utf-8");
    const httpsServer = https.createServer({key: privateKey, cert: certificate});
    const wss = new WebSocket.Server({server: httpsServer});
    // 浏览器和乐聊通讯
    wss.on("connection", ws => {
      ws.on("message", async msg => {
        let thisMsg;
        let logFlag = false;
        win.window.console.log("msg:" + msg);
        try {
          msg = JSON.parse(msg);
          let returnMsg = {type: msg.type, success: true};
          try {
            switch (msg.type) {
              case "open":
                // 获取设备信息
                thisMsg = await win.window.store.dispatch("getSecretEnCrypt", {param: {}});
                ws.send(thisMsg);
                break;
              case "attendCheck":
                // 考勤获取远程工具
                returnMsg.data = await win.window.nw.utils.getRemoteStatus();
                ws.send(JSON.stringify(returnMsg));
                break;
              case "version":
                // 获取乐聊版本号
                returnMsg.data = packageInfo.currentVersion;
                ws.send(JSON.stringify(returnMsg));
                break;
              case "openChat":
                // 打开会话
                returnMsg = await win.window.store.dispatch("browserOpenChat", msg.value);
                ws.send(JSON.stringify(returnMsg));
                logFlag = true;
                break;
              default:
                // 需升级提示
                returnMsg.status = -1;
                returnMsg.success = false;
                ws.send(JSON.stringify(returnMsg));
                logFlag = true;
                break;
            }
          } catch (e) {
            // 相应异常
            returnMsg.success = false;
            returnMsg.errorMsg = e.message;
            ws.send(JSON.stringify(returnMsg));
            logFlag = true;
            msg.errorMsg = e.message;
          }
        } catch (e) {
          logFlag = true;
          msg.errorMsg = e.message;
        }
        if (logFlag) {
          win.window.store.dispatch("uploadZxp", {type: 2, msg: JSON.stringify(msg)});
        }
      });
      ws.on("close", () => {
        win.window.console.log("ws close");
      });
    });
    wss.on("listening", () => {
      win.window.console.log("wss successfully");
    });
    wss.on("close", () => {
      win.window.console.log("wssClose");
    });
    wss.on("error", (err) => {
      win.window.console.log("wssErr", err);
      wssCount++;
      if (wssCount == 2) {
        wssPortRestart(win, port);
      }
    });
    httpsServer.listen(port);
    global.httpsServer = httpsServer;
    // 重启后部分电脑无法打开监听延迟判断重启监听
    setTimeout(() => {
      if (!httpsServer.listening) {
        httpsServer.listen(port);
      }
    }, 1000);
  } catch (e) {
    win.window.console.log("initWssErr", e);
  }
}

// wss端口被占用结束进程重启wss
function wssPortRestart(win, port) {
  let encoding = "gbk";
  let binaryEncoding = "binary";
  cp.exec(`netstat -ano | findstr :${port} | find "LISTENING"`, {encoding: binaryEncoding}, function (err, stdout, stderr) {
    let data = iconv.decode(new Buffer(stderr || stdout, binaryEncoding), encoding);
    let dataList = data.trim().replace(/\n/g, "~~").split("~~");
    let portMap = {};
    // 获取被占用的端口对象
    dataList.map(item => {
      let itemPort = item.split("LISTENING")[1].trim();
      portMap[itemPort] = itemPort;
    });
    let p = [];
    // 结束对应端口程序进程
    for (let key in portMap) {
      p.push(new Promise(resolve => {
        cp.exec(`taskkill /F /PID ${portMap[key]}`, {encoding: binaryEncoding}, function (err, stdout, stderr) {
          resolve();
        });
      }))
    }
    // 等待所有端口结束后再尝试重启
    Promise.all(p).then(res => {
      initWss(win);
    });
  });
}

// 判断host文件写入通讯关联
function setHosts(win) {
  try {
    let hostsPath = process.env.SystemRoot + "\\System32\\drivers\\etc\\hosts";
    if (fs.statSync(hostsPath)) {
      let hosts = fs.readFileSync(hostsPath, "utf-8");
      // hosts不存在关联则写入
      if (!/127\.0\.0\.1\s+localhost\.leyoujia\.com/.test(hosts)) {
        win.window.console.log("setHosts");
        hosts += "\n127.0.0.1 localhost.leyoujia.com\n";
        fs.writeFileSync(hostsPath, hosts, "utf8");
      }
    }
  } catch (e) {
    win.window.console.log("setHostsErr", e);
  }
}

// 删除文件
function unlinkSync(filePath) {
  try {
    fs.unlinkSync(filePath);
  } catch (e) {}
}