# Mac ARM 版本转换说明

本文档说明如何将Windows版本的乐聊客户端转换为Mac ARM版本。

## 主要变更

### 1. 平台检测和兼容性
- 新增 `src/utils/platform.js` - 平台检测和跨平台工具
- 更新 `src/utils/index.js` - 添加Mac兼容的系统信息获取
- 新增 `src/utils/systemInfoMac.js` - Mac系统信息获取工具

### 2. 截图功能替换
- 新增 `src/utils/sdk/jtSDKMac.js` - Mac原生截图功能
- 替换Windows Snipaste工具为Mac系统截图API
- 支持区域截图、全屏截图、窗口截图

### 3. 构建系统
- 新增 `buildFileMac.js` - Mac专用构建脚本
- 更新 `package.json` - 添加Mac构建命令
- 更新 `vue.config.js` - 支持Mac构建目标
- 新增 `cross-env` 依赖用于环境变量管理

### 4. 应用入口
- 新增 `backgroundMac.js` - Mac兼容的应用入口
- 替换Windows特定的进程管理和系统调用
- 适配Mac的文件路径和权限系统

### 5. 进程管理
- 新增 `kill.sh` - Mac版本的进程终止脚本
- 替换Windows批处理文件为Shell脚本

## 构建说明

### 环境要求
- Node.js 14.18.0+
- npm 6.14.15+
- macOS 10.14+
- NW.js v0.100.0-osx-arm64

### 安装依赖
```bash
npm install
```

### 构建Mac版本
```bash
# 构建Mac ARM版本
npm run build:mac env=dev version=*******

# 仅构建Vue部分
npm run buildVue:mac

# 仅执行Mac打包
npm run buildFile:mac
```

### 开发模式
```bash
# 启动开发服务器
npm run serve

# 使用Mac版本的background.js
cp backgroundMac.js background.js
```

## 文件结构变更

### 新增文件
```
├── backgroundMac.js              # Mac版本应用入口
├── buildFileMac.js              # Mac构建脚本
├── kill.sh                      # Mac进程终止脚本
├── README_MAC.md                # Mac版本说明文档
├── src/utils/platform.js       # 平台检测工具
├── src/utils/systemInfoMac.js   # Mac系统信息工具
├── src/utils/sdk/jtSDKMac.js    # Mac截图SDK
└── release/mac-arm64/           # Mac应用输出目录
```

### 修改文件
```
├── package.json                 # 添加Mac构建脚本和依赖
├── vue.config.js               # 支持Mac构建目标
└── src/utils/index.js          # 添加跨平台兼容性
```

## 功能对比

| 功能 | Windows版本 | Mac版本 |
|------|-------------|---------|
| 截图工具 | Snipaste | macOS原生截图 |
| 系统信息 | WMI/PowerShell | system_profiler |
| 进程管理 | taskkill | pkill/kill |
| 路径处理 | 反斜杠(\) | 正斜杠(/) |
| 更新机制 | .exe安装包 | .dmg/.zip |
| 权限管理 | Windows UAC | macOS权限系统 |

## 已知限制

### 1. 截图功能
- Mac版本使用系统原生截图，可能与Snipaste功能略有差异
- 需要用户授权屏幕录制权限

### 2. 系统信息
- 某些Windows特有的硬件信息在Mac上不可用
- 使用Mac等效信息替代

### 3. 自动更新
- Mac版本的自动更新机制需要适配.dmg格式
- 可能需要代码签名才能正常安装

### 4. 文件权限
- Mac版本修改hosts文件可能需要管理员权限
- 某些系统级操作需要用户授权

## 部署说明

### 1. NW.js运行时
需要下载并替换为Mac ARM版本的NW.js运行时：
```bash
# 下载nwjs-v0.100.0-osx-arm64
# 解压到项目根目录，替换现有的nw可执行文件
```

### 2. 应用打包
构建完成后，应用包位于：
```
release/mac-arm64/乐聊.app
```

### 3. 分发
- 可以直接分发.app文件
- 建议打包为.dmg格式便于分发
- 生产环境建议进行代码签名

## 测试建议

### 1. 功能测试
- [ ] 应用启动和基本界面
- [ ] 截图功能（区域、全屏、窗口）
- [ ] 系统信息获取
- [ ] 网络通信功能
- [ ] 文件上传下载
- [ ] 自动更新机制

### 2. 兼容性测试
- [ ] macOS 10.14+
- [ ] Intel Mac（通过Rosetta）
- [ ] Apple Silicon Mac（原生）

### 3. 性能测试
- [ ] 内存使用情况
- [ ] CPU使用率
- [ ] 启动时间
- [ ] 响应速度

## 故障排除

### 1. 权限问题
如果遇到权限错误：
```bash
# 给予执行权限
chmod +x kill.sh
chmod +x 乐聊.app/Contents/MacOS/乐聊
```

### 2. 截图权限
在系统偏好设置 > 安全性与隐私 > 隐私 > 屏幕录制中添加应用权限

### 3. 网络权限
在系统偏好设置 > 安全性与隐私 > 防火墙中允许应用网络访问

## 后续优化

1. **代码签名**: 添加Apple开发者证书签名
2. **公证**: 通过Apple公证流程
3. **自动更新**: 完善Mac版本的自动更新机制
4. **性能优化**: 针对Apple Silicon进行性能优化
5. **UI适配**: 适配Mac的UI设计规范

## 联系方式

如有问题请联系开发团队。
