#!/bin/bash

# Mac ARM 环境设置脚本
echo "=== 乐聊 Mac ARM 版本环境设置 ==="

# 检查系统架构
ARCH=$(uname -m)
echo "当前系统架构: $ARCH"

if [[ "$ARCH" != "arm64" ]]; then
    echo "警告: 当前系统不是ARM64架构，可能需要通过Rosetta运行"
fi

# 检查Node.js版本
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    echo "Node.js版本: $NODE_VERSION"
    
    # 检查版本是否符合要求
    REQUIRED_VERSION="v14.18.0"
    if [[ "$NODE_VERSION" < "$REQUIRED_VERSION" ]]; then
        echo "警告: Node.js版本过低，建议升级到 $REQUIRED_VERSION 或更高版本"
    fi
else
    echo "错误: 未找到Node.js，请先安装Node.js"
    exit 1
fi

# 检查npm版本
if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version)
    echo "npm版本: $NPM_VERSION"
else
    echo "错误: 未找到npm"
    exit 1
fi

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p dist/mac-arm64-unpacked
mkdir -p release/mac-arm64
mkdir -p logs

# 设置脚本权限
echo "设置脚本权限..."
chmod +x kill.sh
chmod +x setup-mac.sh

# 检查是否存在NW.js ARM64版本
if [[ ! -f "nw" ]]; then
    echo "警告: 未找到NW.js可执行文件"
    echo "请下载 nwjs-v0.100.0-osx-arm64 并解压到项目根目录"
    echo "下载地址: https://nwjs.io/downloads/"
fi

# 安装依赖
echo "安装npm依赖..."
npm install

# 检查关键依赖
echo "检查关键依赖..."
REQUIRED_DEPS=("cross-env" "archiver" "terser")
for dep in "${REQUIRED_DEPS[@]}"; do
    if npm list "$dep" &> /dev/null; then
        echo "✓ $dep 已安装"
    else
        echo "✗ $dep 未安装，正在安装..."
        npm install "$dep" --save-dev
    fi
done

# 复制Mac版本的background.js
echo "设置Mac版本配置..."
if [[ -f "backgroundMac.js" ]]; then
    cp backgroundMac.js background.js
    echo "✓ 已切换到Mac版本的background.js"
else
    echo "✗ 未找到backgroundMac.js文件"
fi

# 检查系统权限
echo "检查系统权限..."

# 检查屏幕录制权限（截图功能需要）
echo "注意: 截图功能需要屏幕录制权限"
echo "请在 系统偏好设置 > 安全性与隐私 > 隐私 > 屏幕录制 中添加应用权限"

# 检查网络权限
echo "注意: 应用需要网络访问权限"
echo "如果遇到网络问题，请在 系统偏好设置 > 安全性与隐私 > 防火墙 中允许应用"

# 显示构建命令
echo ""
echo "=== 环境设置完成 ==="
echo ""
echo "可用的构建命令:"
echo "  npm run serve                    # 启动开发服务器"
echo "  npm run build:mac env=dev version=4.0.0.0  # 构建Mac版本"
echo "  npm run buildVue:mac            # 仅构建Vue部分"
echo "  npm run buildFile:mac           # 仅执行Mac打包"
echo ""
echo "构建完成后，应用包位于: release/mac-arm64/乐聊.app"
echo ""
echo "如有问题，请查看 README_MAC.md 文档"
