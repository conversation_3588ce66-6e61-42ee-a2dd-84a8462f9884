const archiver = require("archiver");
const glob = require("glob");
const path = require("path");
const fs = require("fs");
const childProcess = require("child_process");
const terser = require("terser");
const cp = require("child_process");

let env = getArg("env");
let version = getArg("version");

let p1 = nwjc("index");
let p2 = nwjc("forward");
let p3 = nwjc("notify");
let p4 = nwjc("viewer");
let p5 = nwjc("shortcutConflict");
let p6 = nwjc("netDetect");
let p7 = nwjc("js/jsqr");
let p8 = nwjc("js/public");
let p9 = nwjc("js/captcha");

let p101 = nwjc("chunk-index");
let p102 = nwjc("chunk-child");
let p103 = nwjc("chunk-common");
let p104 = nwjc("chunk-iconv");
let p105 = nwjc("chunk-dexie");

Promise.all([p1, p2, p3, p4, p5, p6, p7, p8, p9, p101, p102, p103, p104, p105]).then(function () {
  // 删除历史文件
  unlinkDir(path.join(__dirname, "/dist/nw"));
  
  // 生成文件夹
  mkdirsSync(path.join(__dirname, "/dist/mac-arm64-unpacked/"));
  mkdirsSync(path.join(__dirname, "/dist/mac-arm64-unpacked/node_modules/adm-zip/"));
  
  // 新增代理request依赖
  setRequestFile();
  
  // 复制到压缩包目录
  copyFile(path.join(__dirname, "/package.json"), path.join(__dirname, "/dist/mac-arm64-unpacked/package.json"));
  
  let p = [
    uglifyJs(path.join(__dirname, "/config.js"), path.join(__dirname, "/dist/mac-arm64-unpacked/config.js"), false),
    uglifyJs(path.join(__dirname, "/background.js"), path.join(__dirname, "/dist/mac-arm64-unpacked/background.js"), true),
  ];
  
  Promise.allSettled(p).then(res => {
    // 生成文件夹
    mkdirsSync(path.join(__dirname, "/dist/nw/"));
    mkdirsSync(path.join(__dirname, "/dist/nw/node_modules/adm-zip/"));
    
    // 复制到打包目录
    copyAllFile(path.join(__dirname, "/node_modules/adm-zip"), path.join(__dirname, "/dist/nw/node_modules/adm-zip"));
    copyAllFile(path.join(__dirname, "/dist/mac-arm64-unpacked"), path.join(__dirname, "/dist/nw"));
    
    // 复制Mac相关文件到目录
    let fileReg = /\.bin|(\.json&!temp.json)|\.pak|\.dat|\.png|\.pem|\.key|\.dylib/;
    let dirReg = /locales/;
    copyAllFile(path.join(__dirname), path.join(__dirname, "/dist/nw"), fileReg, dirReg);

    // 打包压缩包
    createZip();

    // 创建Mac应用包
    createMacApp();
  });
});

// 复制文件/目录
function copyAllFile(src, dist, fileReg, dirReg) {
  let files, stat, _src, _dist;
  files = fs.readdirSync(src);
  files.forEach(function (file) {
    _src = src + path.sep + file;
    _dist = dist + path.sep + file;
    stat = fs.statSync(_src);
    if (stat.isFile()) {
      if (!fileReg || fileReg.test(file)) {
        console.log(_src, "==========>", _dist);
        copyFile(_src, _dist);
      }
    } else {
      if (!dirReg || dirReg.test(file)) {
        mkdirsSync(_dist);
        copyAllFile(_src, _dist, fileReg, dirReg);
      }
    }
  });
}

function copyFile(_src, _dist) {
  try {
    fs.writeFileSync(_dist, fs.readFileSync(_src));
  } catch (error) {
    console.error("copyFile", error);
    fs.unlinkSync(_dist);
    fs.writeFileSync(_dist, fs.readFileSync(_src));
  }
}

// 压缩混淆js文件
async function uglifyJs(fileIn, fileOut, mangle) {
  let readCode = fs.readFileSync(fileIn, "utf8");
  let res = await terser.minify(readCode, {mangle: mangle});
  fs.writeFileSync(fileOut, res.code, "utf8");
}

// 递归创建目录 同步方法
function mkdirsSync(dirname) {
  if (fs.existsSync(dirname)) {
    return true;
  } else {
    if (mkdirsSync(path.dirname(dirname))) {
      fs.mkdirSync(dirname);
      return true;
    }
  }
}

// 删除目录
function unlinkDir(dir) {
  try {
    let files = fs.readdirSync(dir);
    files.forEach(function (file) {
      let stats = fs.statSync(`${dir}/${file}`);
      if (stats.isDirectory()) {
        unlinkDir(`${dir}/${file}`);
      } else {
        fs.unlinkSync(`${dir}/${file}`);
      }
    });
    fs.rmdirSync(dir);
  } catch (error) {
    console.error("unlinkDir", error);
  }
}

// 打包zip
function createZip() {
  let dirPath = path.join(__dirname, "/dist/mac-arm64-unpacked");
  let targetPath = path.join(__dirname, "/dist/");
  let targetName = "mac-arm64-unpacked.zip"
  let output = fs.createWriteStream(targetPath + targetName);
  let archive = archiver("zip", {
    store: true
  });

  output.on("close", function () {
    console.log("Mac ZIP created successfully");
  });

  archive.on("error", function (err) {
    throw err;
  });

  let files = glob.sync("**/*.*", {
    cwd: dirPath,
    dot: false
  });

  files.forEach(function (file) {
    if (file != "app.asar.unpacked") {
      let filePath = path.resolve(dirPath, file);
      console.log("[archive] " + filePath);
      archive.append(fs.createReadStream(filePath), {
        "name": file,
        "mode": fs.statSync(filePath).mode
      });
    }
  });

  archive.pipe(output);
  archive.finalize();
}

// 创建Mac应用包
function createMacApp() {
  let appName = "乐聊";
  let theEnv = (env == "online" ? "" : `-${env}`);
  let appPath = path.join(__dirname, "/release/mac-arm64/", appName + theEnv + ".app");
  
  // 创建应用包结构
  mkdirsSync(path.join(appPath, "Contents/MacOS"));
  mkdirsSync(path.join(appPath, "Contents/Resources"));
  
  // 复制应用文件
  copyAllFile(path.join(__dirname, "/dist/nw"), path.join(appPath, "Contents/Resources"));
  
  // 创建Info.plist
  createInfoPlist(appPath, appName + theEnv);
  
  // 创建启动脚本
  createLaunchScript(appPath, appName + theEnv);
  
  console.log("Mac app created at:", appPath);
}

// 创建Info.plist文件
function createInfoPlist(appPath, appName) {
  let plistContent = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleDisplayName</key>
    <string>${appName}</string>
    <key>CFBundleExecutable</key>
    <string>${appName}</string>
    <key>CFBundleIdentifier</key>
    <string>com.leyoujia.im</string>
    <key>CFBundleName</key>
    <string>${appName}</string>
    <key>CFBundleVersion</key>
    <string>${version || "*******"}</string>
    <key>CFBundleShortVersionString</key>
    <string>${version || "*******"}</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>LSMinimumSystemVersion</key>
    <string>10.14</string>
    <key>NSHighResolutionCapable</key>
    <true/>
    <key>NSSupportsAutomaticGraphicsSwitching</key>
    <true/>
</dict>
</plist>`;
  
  fs.writeFileSync(path.join(appPath, "Contents/Info.plist"), plistContent);
}

// 创建启动脚本
function createLaunchScript(appPath, appName) {
  let scriptContent = `#!/bin/bash
cd "$(dirname "$0")/../Resources"
./nw .
`;
  
  let scriptPath = path.join(appPath, "Contents/MacOS", appName);
  fs.writeFileSync(scriptPath, scriptContent);
  fs.chmodSync(scriptPath, 0o755);
}

// 获取参数
function getArg(key) {
  let argv = process.argv;
  let index = argv.findIndex(item => new RegExp(key).test(item))
  if (index != -1) {
    return argv[index].split("=")[1]
  }
  return ""
}

// 新增代理request依赖
function setRequestFile() {
  let fileList = [
    ".bin", "ajv", "asn1", "assert-plus", "asynckit", "aws4", "aws-sign2", "bcrypt-pbkdf", "caseless", "combined-stream", "core-util-is", "dashdash", "delayed-stream",
    "ecc-jsbn", "extend", "extsprintf", "fast-deep-equal", "fast-json-stable-stringify", "forever-agent", "form-data", "getpass", "har-schema", "har-validator",
    "http-signature", "isstream", "is-typedarray", "jsbn", "json-schema", "json-schema-traverse", "json-stringify-safe", "jsprim", "mime-db", "mime-types", "oauth-sign",
    "performance-now", "psl", "punycode", "qs", "request", "safe-buffer", "safer-buffer", "sshpk", "tough-cookie", "tunnel-agent", "tweetnacl", "uri-js", "uuid", "verror",
    "ws", "async-limiter", "iconv-lite", "heic2any",
  ];
  fileList.map(item => {
    mkdirsSync(path.join(__dirname, `/dist/mac-arm64-unpacked/node_modules/${item}/`));
    copyAllFile(path.join(__dirname, `/node_modules/${item}`), path.join(__dirname, `/dist/mac-arm64-unpacked/node_modules/${item}`));
    mkdirsSync(path.join(__dirname, `/dist/nw/node_modules/${item}/`));
    copyAllFile(path.join(__dirname, `/node_modules/${item}`), path.join(__dirname, `/dist/nw/node_modules/${item}`));
  });
}

// nwjc加密文件
function nwjc(fileName) {
  return new Promise(function (resolve) {
    let fileDir = `${__dirname}/dist/mac-arm64-unpacked/`;
    let filePath = fileDir + fileName + ".js";
    cp.exec(`nwjc ${filePath} ${fileDir}${fileName}.bin`, function (err, stdout, stderr) {
      if (stderr) {
        console.log("nwjcErr", stderr);
        return;
      }
      fs.unlinkSync(filePath);
      resolve();
    });
  });
}
